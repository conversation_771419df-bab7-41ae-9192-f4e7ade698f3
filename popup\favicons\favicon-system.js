/**
 * Sistema de Favicons para Bookmark Extension
 *
 * Sistema simplificado que carrega favicons usando as APIs do navegador
 * e mantém um cache em memória para performance.
 */

// Namespace global para o sistema de favicons
window.FaviconSystem = window.FaviconSystem || {};

(function() {
    'use strict';

    // Configurações do sistema
    const CONFIG = {
        FAVICON_SIZE: 16,
        FALLBACK_FAVICON: '../img/icons/star_blue_ext.png',
        LOAD_TIMEOUT: 5000,
        CACHE_DURATION: 7 * 24 * 60 * 60 * 1000, // 7 dias
        STORAGE_KEY: 'faviconCache_simple'
    };

    // Cache em memória
    let faviconCache = new Map();
    let isInitialized = false;

    // ID da extensão (será detectado automaticamente)
    let extensionId = 'hcffhpomdbeehbcleoijdcnkfencblng';

    // Detectar navegador
    let currentBrowser = 'chrome';
    if (navigator.userAgent.toLowerCase().includes('edg/')) {
        currentBrowser = 'edge';
    }

    /**
     * Detecta o ID da extensão automaticamente
     */
    function detectExtensionId() {
        if (chrome.runtime && chrome.runtime.id) {
            extensionId = chrome.runtime.id;
            console.log(`[FaviconSystem] ID da extensão detectado: ${extensionId}`);
        } else {
            console.log(`[FaviconSystem] Usando ID fixo da extensão: ${extensionId}`);
        }
    }

    /**
     * Gera URL do favicon usando a API da extensão
     * @param {string} url - URL do site
     * @returns {string} URL do favicon
     */
    function getFaviconUrl(url) {
        if (!url) return CONFIG.FALLBACK_FAVICON;

        try {
            // URLs internas do navegador não têm favicon
            if (url.startsWith('chrome://') ||
                url.startsWith('edge://') ||
                url.startsWith('about:') ||
                url.startsWith('browser://') ||
                url.startsWith('chrome-extension://') ||
                url.startsWith('moz-extension://')) {
                return CONFIG.FALLBACK_FAVICON;
            }

            // Usar API da extensão para favicons
            return `chrome-extension://${extensionId}/_favicon/?pageUrl=${encodeURIComponent(url)}&size=${CONFIG.FAVICON_SIZE}`;

        } catch (error) {
            console.warn(`[FaviconSystem] URL inválida: ${url}`, error);
            return CONFIG.FALLBACK_FAVICON;
        }
    }

    /**
     * Gera chave de cache baseada na URL
     * @param {string} url - URL do site
     * @returns {string} Chave do cache
     */
    function getCacheKey(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch (error) {
            return url;
        }
    }

    /**
     * Carrega favicon com timeout
     * @param {string} faviconUrl - URL do favicon
     * @returns {Promise<boolean>} True se carregou com sucesso
     */
    function loadFaviconWithTimeout(faviconUrl) {
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                resolve(false);
            }, CONFIG.LOAD_TIMEOUT);

            const img = new Image();

            img.onload = () => {
                clearTimeout(timeout);
                resolve(true);
            };

            img.onerror = () => {
                clearTimeout(timeout);
                resolve(false);
            };

            img.src = faviconUrl;
        });
    }

    /**
     * Carrega um favicon para um elemento img
     * @param {HTMLImageElement} imgElement - Elemento img para carregar o favicon
     * @param {string} url - URL do site
     */
    async function loadFavicon(imgElement, url) {
        if (!imgElement || !url) {
            console.warn('[FaviconSystem] Parâmetros inválidos para loadFavicon');
            return;
        }

        const cacheKey = getCacheKey(url);

        // Verificar cache primeiro
        if (faviconCache.has(cacheKey)) {
            const cachedEntry = faviconCache.get(cacheKey);
            if (Date.now() - cachedEntry.timestamp < CONFIG.CACHE_DURATION) {
                imgElement.src = cachedEntry.faviconUrl;
                return;
            } else {
                // Cache expirado
                faviconCache.delete(cacheKey);
            }
        }

        // Definir favicon padrão temporariamente
        imgElement.src = CONFIG.FALLBACK_FAVICON;

        // Gerar URL do favicon
        const faviconUrl = getFaviconUrl(url);

        // Se for o fallback, não precisa carregar
        if (faviconUrl === CONFIG.FALLBACK_FAVICON) {
            return;
        }

        try {
            // Tentar carregar o favicon
            const success = await loadFaviconWithTimeout(faviconUrl);

            if (success) {
                // Sucesso - atualizar elemento e cache
                imgElement.src = faviconUrl;

                faviconCache.set(cacheKey, {
                    faviconUrl: faviconUrl,
                    timestamp: Date.now(),
                    originalUrl: url
                });

                // Salvar no storage (debounced)
                debouncedSaveCache();
            } else {
                // Falha - manter fallback e cachear para evitar tentativas futuras
                faviconCache.set(cacheKey, {
                    faviconUrl: CONFIG.FALLBACK_FAVICON,
                    timestamp: Date.now(),
                    originalUrl: url
                });
            }
        } catch (error) {
            console.warn(`[FaviconSystem] Erro ao carregar favicon para ${url}:`, error);
        }
    }

    // Debounced save para evitar muitas escritas no storage
    let saveTimeout;
    function debouncedSaveCache() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
            const cacheObj = {};
            for (const [key, value] of faviconCache.entries()) {
                cacheObj[key] = value;
            }

            chrome.storage.local.set({
                [CONFIG.STORAGE_KEY]: cacheObj
            }, () => {
                if (chrome.runtime.lastError) {
                    console.warn('[FaviconSystem] Erro ao salvar cache:', chrome.runtime.lastError);
                } else {
                    console.log(`[FaviconSystem] Cache salvo: ${faviconCache.size} entradas`);
                }
            });
        }, 2000);
    }

    /**
     * Carrega o cache do armazenamento
     */
    async function loadCacheFromStorage() {
        return new Promise((resolve) => {
            chrome.storage.local.get([CONFIG.STORAGE_KEY], (result) => {
                if (chrome.runtime.lastError) {
                    console.warn('[FaviconSystem] Erro ao carregar cache:', chrome.runtime.lastError);
                    resolve();
                    return;
                }

                const cacheData = result[CONFIG.STORAGE_KEY] || {};
                const now = Date.now();

                for (const [key, entry] of Object.entries(cacheData)) {
                    if (entry.timestamp && (now - entry.timestamp) < CONFIG.CACHE_DURATION) {
                        faviconCache.set(key, entry);
                    }
                }

                console.log(`[FaviconSystem] Cache carregado: ${faviconCache.size} entradas`);
                resolve();
            });
        });
    }

    /**
     * Inicializa o sistema de favicons
     */
    async function init() {
        if (isInitialized) {
            return;
        }

        console.log('[FaviconSystem] Inicializando sistema de favicons...');

        // Detectar ID da extensão
        detectExtensionId();

        await loadCacheFromStorage();

        isInitialized = true;
        console.log('[FaviconSystem] Sistema de favicons inicializado');
        console.log(`[FaviconSystem] Usando API: chrome-extension://${extensionId}/_favicon/`);
    }

    /**
     * Limpa todo o cache
     */
    function clearCache() {
        faviconCache.clear();
        chrome.storage.local.remove([CONFIG.STORAGE_KEY], () => {
            console.log('[FaviconSystem] Cache limpo completamente');
        });
    }

    /**
     * Obtém estatísticas do cache
     */
    function getCacheStats() {
        return {
            cacheSize: faviconCache.size,
            browser: currentBrowser,
            initialized: isInitialized
        };
    }

    // Expor API pública
    window.FaviconSystem = {
        init,
        loadFavicon,
        clearCache,
        getCacheStats,
        getFaviconUrl,

        // Configurações (somente leitura)
        config: Object.freeze({...CONFIG})
    };

    // Auto-inicializar quando o DOM estiver pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        // DOM já está pronto
        setTimeout(init, 0);
    }

})();
