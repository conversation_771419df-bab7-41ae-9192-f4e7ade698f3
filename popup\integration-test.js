/**
 * Teste de Integração dos Sistemas Unificados
 * Verifica se os sistemas estão funcionando corretamente após as mudanças
 */

window.IntegrationTest = window.IntegrationTest || {};

(function() {
    'use strict';

    /**
     * Executa todos os testes de integração
     */
    function runAllTests() {
        console.log('=== INICIANDO TESTES DE INTEGRAÇÃO ===');
        
        const results = {
            passed: 0,
            failed: 0,
            tests: []
        };

        // Lista de testes
        const tests = [
            testUnifiedSelectionSystem,
            testBookmarkElementFactory,
            testDynamicBookmarkRenderer,
            testRenderSystem,
            testSynchronization
        ];

        // Executar cada teste
        tests.forEach(test => {
            try {
                const result = test();
                results.tests.push(result);
                if (result.passed) {
                    results.passed++;
                } else {
                    results.failed++;
                }
            } catch (error) {
                console.error(`Erro ao executar teste ${test.name}:`, error);
                results.tests.push({
                    name: test.name,
                    passed: false,
                    message: `Erro: ${error.message}`
                });
                results.failed++;
            }
        });

        // Mostrar resultados
        console.log('=== RESULTADOS DOS TESTES ===');
        results.tests.forEach(test => {
            const status = test.passed ? '✅' : '❌';
            console.log(`${status} ${test.name}: ${test.message}`);
        });

        console.log(`\n📊 RESUMO: ${results.passed} passou(m), ${results.failed} falhou(ram)`);
        
        return results;
    }

    /**
     * Testa o sistema unificado de seleção
     */
    function testUnifiedSelectionSystem() {
        const testName = 'Sistema Unificado de Seleção';
        
        if (!window.UnifiedBookmarkSelection) {
            return { name: testName, passed: false, message: 'UnifiedBookmarkSelection não encontrado' };
        }

        // Verificar se as funções principais existem
        const requiredFunctions = [
            'init',
            'updateBookmarkSelection',
            'getSelectedBookmarkIds',
            'clearBookmarkSelection',
            'registerSyncCallback'
        ];

        for (const func of requiredFunctions) {
            if (typeof window.UnifiedBookmarkSelection[func] !== 'function') {
                return { name: testName, passed: false, message: `Função ${func} não encontrada` };
            }
        }

        // Verificar se as variáveis globais existem
        if (!window.selectedBookmarkIds) {
            return { name: testName, passed: false, message: 'selectedBookmarkIds global não encontrada' };
        }

        return { name: testName, passed: true, message: 'Todas as funções e variáveis estão disponíveis' };
    }

    /**
     * Testa o factory de elementos de bookmark
     */
    function testBookmarkElementFactory() {
        const testName = 'Bookmark Element Factory';
        
        if (!window.BookmarkElementFactory) {
            return { name: testName, passed: false, message: 'BookmarkElementFactory não encontrado' };
        }

        // Verificar se as funções principais existem
        const requiredFunctions = [
            'createBookmarkElement',
            'setupBookmarkEvents',
            'updateBookmarkElement',
            'loadFavicon'
        ];

        for (const func of requiredFunctions) {
            if (typeof window.BookmarkElementFactory[func] !== 'function') {
                return { name: testName, passed: false, message: `Função ${func} não encontrada` };
            }
        }

        // Testar criação de elemento
        try {
            const mockBookmark = {
                id: 'test-bookmark-1',
                title: 'Teste',
                url: 'https://example.com',
                index: 0
            };

            const element = window.BookmarkElementFactory.createBookmarkElement(mockBookmark, 'test-folder');
            
            if (!element || !element.classList.contains('bookmark-item')) {
                return { name: testName, passed: false, message: 'Elemento criado não tem estrutura correta' };
            }

            // Verificar se tem checkbox
            const checkbox = element.querySelector('.bookmark-checkbox');
            if (!checkbox) {
                return { name: testName, passed: false, message: 'Checkbox não encontrado no elemento' };
            }

        } catch (error) {
            return { name: testName, passed: false, message: `Erro ao criar elemento: ${error.message}` };
        }

        return { name: testName, passed: true, message: 'Factory funcionando corretamente' };
    }

    /**
     * Testa o sistema de renderização dinâmica
     */
    function testDynamicBookmarkRenderer() {
        const testName = 'Dynamic Bookmark Renderer';
        
        if (!window.DynamicBookmarkRenderer) {
            return { name: testName, passed: false, message: 'DynamicBookmarkRenderer não encontrado' };
        }

        // Verificar se as funções principais existem
        const requiredFunctions = [
            'init',
            'loadBookmarksFromFolders',
            'getSelectedBookmarks',
            'setSelectedBookmarks'
        ];

        for (const func of requiredFunctions) {
            if (typeof window.DynamicBookmarkRenderer[func] !== 'function') {
                return { name: testName, passed: false, message: `Função ${func} não encontrada` };
            }
        }

        return { name: testName, passed: true, message: 'Todas as funções estão disponíveis' };
    }

    /**
     * Testa o sistema de renderização antigo
     */
    function testRenderSystem() {
        const testName = 'Sistema de Renderização Antigo';
        
        // Verificar se as funções principais existem
        const requiredFunctions = [
            'createBookmarkElement',
            'renderBookmarks'
        ];

        for (const func of requiredFunctions) {
            if (typeof window[func] !== 'function') {
                return { name: testName, passed: false, message: `Função ${func} não encontrada` };
            }
        }

        return { name: testName, passed: true, message: 'Funções de renderização disponíveis' };
    }

    /**
     * Testa a sincronização entre sistemas
     */
    function testSynchronization() {
        const testName = 'Sincronização entre Sistemas';
        
        if (!window.UnifiedBookmarkSelection || !window.DynamicBookmarkRenderer) {
            return { name: testName, passed: false, message: 'Sistemas necessários não encontrados' };
        }

        // Verificar se o callback de sincronização pode ser registrado
        try {
            let callbackCalled = false;
            const testCallback = (eventType, data) => {
                callbackCalled = true;
            };

            window.UnifiedBookmarkSelection.registerSyncCallback(testCallback);
            
            // Simular mudança de seleção
            if (window.selectedBookmarkIds) {
                window.selectedBookmarkIds.add('test-sync');
                window.selectedBookmarkIds.delete('test-sync');
            }

            // Remover callback
            window.UnifiedBookmarkSelection.unregisterSyncCallback(testCallback);

        } catch (error) {
            return { name: testName, passed: false, message: `Erro ao testar sincronização: ${error.message}` };
        }

        return { name: testName, passed: true, message: 'Sistema de sincronização configurado' };
    }

    /**
     * Testa a compatibilidade com código existente
     */
    function testBackwardCompatibility() {
        const testName = 'Compatibilidade com Código Existente';
        
        // Verificar se as funções globais ainda existem
        const requiredGlobalFunctions = [
            'initBookmarkSelection',
            'updateBookmarkSelection',
            'getSelectedBookmarkIds',
            'updateSelectedBookmarksCount'
        ];

        for (const func of requiredGlobalFunctions) {
            if (typeof window[func] !== 'function') {
                return { name: testName, passed: false, message: `Função global ${func} não encontrada` };
            }
        }

        return { name: testName, passed: true, message: 'Compatibilidade mantida' };
    }

    // API pública
    window.IntegrationTest = {
        runAllTests,
        testUnifiedSelectionSystem,
        testBookmarkElementFactory,
        testDynamicBookmarkRenderer,
        testRenderSystem,
        testSynchronization,
        testBackwardCompatibility
    };

    console.log('[IntegrationTest] Sistema de testes carregado');

})();

// Auto-executar testes quando o DOM estiver pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            window.IntegrationTest.runAllTests();
        }, 1000);
    });
} else {
    setTimeout(() => {
        window.IntegrationTest.runAllTests();
    }, 1000);
}
