<!DOCTYPE html>
<html>
<head>
    <title>Teste de Feedback</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { margin: 10px; padding: 10px; }
        .action-feedback {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        .action-feedback.show {
            opacity: 1;
            transform: translateX(0);
        }
        .action-feedback.info { background-color: #2196F3; }
        .action-feedback.success { background-color: #4CAF50; }
        .action-feedback.warning { background-color: #FF9800; }
        .action-feedback.error { background-color: #f44336; }
    </style>
</head>
<body>
    <h1>Teste de Sistema de Feedback</h1>
    <p>Este arquivo testa se o sistema de feedback está funcionando corretamente após desabilitar o folder-monitor.js</p>
    
    <button onclick="testCreated()">Simular Pasta Criada</button>
    <button onclick="testRemoved()">Simular Pasta Removida</button>
    <button onclick="testRenamed()">Simular Pasta Renomeada</button>
    <button onclick="testMoved()">Simular Pasta Movida</button>
    
    <div id="log"></div>

    <script>
        // Função de feedback (copiada do utils.js)
        function showActionFeedback(message, type = 'success', duration = 2000) {
            let feedback = document.querySelector('.action-feedback');
            
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'action-feedback';
                document.body.appendChild(feedback);
            }
            
            feedback.textContent = message;
            feedback.className = `action-feedback ${type}`;
            
            // Mostrar
            setTimeout(() => feedback.classList.add('show'), 10);
            
            // Esconder após duração
            setTimeout(() => {
                feedback.classList.remove('show');
            }, duration);
        }

        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
        }

        function testCreated() {
            showActionFeedback('Nova pasta "Teste" detectada', 'info');
            log('Testou criação de pasta');
        }

        function testRemoved() {
            showActionFeedback('Pasta "Teste" foi removida', 'warning');
            log('Testou remoção de pasta');
        }

        function testRenamed() {
            showActionFeedback('Pasta renomeada: "Teste" → "Novo Nome"', 'info');
            log('Testou renomeação de pasta');
        }

        function testMoved() {
            showActionFeedback('Pasta "Teste" foi movida', 'info');
            log('Testou movimentação de pasta');
        }
    </script>
</body>
</html>
