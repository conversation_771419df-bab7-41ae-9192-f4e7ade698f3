<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bookmark Manager</title>

  <link rel="stylesheet" href="popup.css" />
  <link href="https://fonts.googleapis.com/css2?family=Roboto&display=swap" rel="stylesheet">
  <style>
    /* Estilos movidos para o arquivo popup.css */
  </style>
</head>
<body>
  <main>
    <div class="content-container">
      <!-- Coluna de pastas -->
      <div class="folders-column">
        <div class="column-header">
          <h2 id="folderCount" tabindex="1">Pastas exibidas: 0</h2>
          <h3 id="selectedFoldersCount">Selecionadas: 0</h3>
        </div>

        <!-- Movendo os botões para antes da barra de pesquisa -->
        <div class="folder-header">
          <div class="folder-buttons">
            <button id="selectAllFoldersBtn" class="folder-header-btn" type="button" title="Selecionar todas as pastas visíveis">Selecionar</button>
            <button id="deselectAllFoldersBtn" class="folder-header-btn" type="button" title="Desselecionar todas as pastas visíveis">Desselecionar</button>
          </div>
        </div>

        <!-- Movida a barra de pesquisa para depois dos controles -->
        <div class="search">
          <div class="search-container">
            <input
              class="search-input"
              id="folderSearch"
              type="search"
              placeholder="Pesquisar Pastas"
              accesskey="p"
              tabindex="2"
              data-show-clear="true"
            />
          </div>
        </div>
        
        <div id="folderCheckboxes" class="scrollable-list"></div>
      </div>
      
      <!-- Separador redimensionável -->
      <div class="separator resizable" id="mainSeparator" role="separator"
           aria-orientation="vertical"
           tabindex="0"
           title="Arraste para redimensionar, clique duas vezes para centralizar, ou use as setas ← → do teclado"
           aria-label="Separador redimensionável. Use setas esquerda/direita para mover, clique duas vezes para centralizar, ou teclas Home/End para mínimo/máximo">
      </div>
      
      <!-- Coluna de favoritos -->
      <div class="bookmarks-column">
        <div class="column-header">
          <h2 id="bookmarksDisplayCount" tabindex="3">Favoritos exibidos: 0</h2>
          <h3 id="selectedBookmarksCount">Selecionados: 0</h3>
        </div>

        <!-- Botões para selecionar/desselecionar favoritos -->
        <div class="folder-header">
          <div class="folder-buttons">
            <button id="selectAllBookmarksBtn" class="folder-header-btn" type="button" title="Selecionar todos os favoritos visíveis">Selecionar</button>
            <button id="deselectAllBookmarksBtn" class="folder-header-btn" type="button" title="Desselecionar todos os favoritos visíveis">Desselecionar</button>
          </div>
        </div>

        <!-- Movida a barra de pesquisa para depois do contador -->
        <div class="search">
          <div class="search-container">
            <input
              class="search-input"
              id="bookmarkSearch"
              type="search"
              placeholder="Pesquisar Favoritos"
              accesskey="f"
              tabindex="4"
              data-show-clear="true"
            />
          </div>
        </div>
        
        <div id="bookmarksContainer" class="scrollable-list"></div>
      </div>
    </div>
  </main>

  <!-- Menu de Contexto -->
  <div id="contextMenu" class="context-menu" style="display: none;">
    <!-- Opções para Pasta -->
    <div id="folderContextOptions" class="context-options" style="display: none;">
      <div class="context-item" data-action="sort-folder">
        <span class="context-icon">📋</span>
        <span class="context-text">Classificar por nome</span>
      </div>
      <div class="context-item" data-action="rename-folder">
        <span class="context-icon">✏️</span>
        <span class="context-text">Renomear</span>
      </div>
      <div class="context-item" data-action="create-subfolder">
        <span class="context-icon">📁</span>
        <span class="context-text">Criar subpasta</span>
      </div>
      <div class="context-item context-submenu" data-action="move-folder-submenu">
        <span class="context-icon">📤</span>
        <span class="context-text">Mover para...</span>
        <span class="context-arrow">⯈</span>
        <div class="context-submenu-content">
          <div class="context-item" data-action="move-folder-to-top-current">
            <span class="context-icon">⬆️</span>
            <span class="context-text">Topo da pasta atual</span>
          </div>
          <div class="context-item" data-action="move-folder-to-top-tree">
            <span class="context-icon">🔝</span>
            <span class="context-text">Topo da árvore</span>
          </div>
          <div class="context-separator"></div>
          <div class="context-item" data-action="move-folder-custom">
            <span class="context-icon">📁</span>
            <span class="context-text">Escolher pasta...</span>
          </div>
        </div>
      </div>
      <div class="context-item" data-action="delete-folder">
        <span class="context-icon">🗑️</span>
        <span class="context-text">Excluir</span>
      </div>
      <div class="context-separator"></div>
      <div class="context-item" data-action="open-all">
        <span class="context-icon">🔗</span>
        <span class="context-text" id="openAllText">Abrir todos os favoritos</span>
      </div>
      <div class="context-item" data-action="open-all-new-window">
        <span class="context-icon">🪟</span>
        <span class="context-text" id="openAllNewWindowText">Abrir tudo em nova janela</span>
      </div>
      <div class="context-item" data-action="open-all-incognito">
        <span class="context-icon">🕵️</span>
        <span class="context-text" id="openAllIncognitoText">Abrir todos em nova janela InPrivate</span>
      </div>
      <div class="context-item" data-action="open-all-tab-group">
        <span class="context-icon">📑</span>
        <span class="context-text" id="openAllTabGroupText">Abrir todos em novo grupo de guias</span>
      </div>
    </div>

    <!-- Opções para Favorito -->
    <div id="bookmarkContextOptions" class="context-options" style="display: none;">
      <div class="context-item" data-action="edit-bookmark">
        <span class="context-icon">✏️</span>
        <span class="context-text">Editar</span>
      </div>
      <div class="context-item" data-action="copy-link">
        <span class="context-icon">📋</span>
        <span class="context-text">Copiar link</span>
      </div>
      <div class="context-item" data-action="move-bookmark">
        <span class="context-icon">📤</span>
        <span class="context-text">Mover</span>
      </div>
      <div class="context-item" data-action="delete-bookmark">
        <span class="context-icon">🗑️</span>
        <span class="context-text">Excluir</span>
      </div>
      <div class="context-separator"></div>
      <div class="context-item" data-action="open-new-tab">
        <span class="context-icon">🔗</span>
        <span class="context-text">Abrir em uma nova guia</span>
      </div>
      <div class="context-item" data-action="open-new-window">
        <span class="context-icon">🪟</span>
        <span class="context-text">Abrir em uma nova janela</span>
      </div>
      <div class="context-item" data-action="open-incognito">
        <span class="context-icon">🕵️</span>
        <span class="context-text">Abrir em uma nova janela InPrivate</span>
      </div>
    </div>

    <!-- Opções para Seleção Múltipla -->
    <div id="multipleContextOptions" class="context-options" style="display: none;">
      <div class="context-item" data-action="delete-multiple">
        <span class="context-icon">🗑️</span>
        <span class="context-text" id="deleteMultipleText">Excluir itens</span>
      </div>
      <div class="context-separator"></div>
      <div class="context-item" data-action="open-multiple">
        <span class="context-icon">🔗</span>
        <span class="context-text" id="openMultipleText">Abrir todos</span>
      </div>
      <div class="context-item" data-action="open-multiple-new-window">
        <span class="context-icon">🪟</span>
        <span class="context-text" id="openMultipleNewWindowText">Abrir tudo em nova janela</span>
      </div>
      <div class="context-item" data-action="open-multiple-incognito">
        <span class="context-icon">🕵️</span>
        <span class="context-text" id="openMultipleIncognitoText">Abrir todos em nova janela InPrivate</span>
      </div>
      <div class="context-item" data-action="open-multiple-tab-group">
        <span class="context-icon">📑</span>
        <span class="context-text" id="openMultipleTabGroupText">Abrir todos em novo grupo de guias</span>
      </div>
    </div>
  </div>

  <!-- Diálogo de Edição de Favorito -->
  <div id="editBookmarkDialog" class="edit-dialog" style="display: none;">
    <div class="edit-dialog-content">
      <h3>Editar Favorito</h3>
      <div class="edit-field">
        <label for="editBookmarkName">Nome:</label>
        <input type="text" id="editBookmarkName" class="edit-input">
      </div>
      <div class="edit-field">
        <label for="editBookmarkUrl">URL:</label>
        <input type="text" id="editBookmarkUrl" class="edit-input">
      </div>
      <div class="edit-buttons">
        <button id="createNewFolderBtn" class="secondary-btn">Nova pasta</button>
        <button id="saveBookmarkBtn" class="primary-btn">Salvar</button>
        <button id="cancelEditBtn" class="secondary-btn">Cancelar</button>
      </div>
    </div>
  </div>

  <!-- Rodapé -->
  <footer>
    <div class="footer-left">
      <img class="logo" id="productLogo" src="../img/icons/bfm256.png" alt="logo" />
      <h1>Bookmark Manager & Folder Merger</h1>
    </div>
    <div class="footer-right">
      <button id="themeToggleBtn" class="button-footer" type="button" title="Alternar entre tema claro e escuro">
        <svg id="lightThemeIcon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 14.7614 9.23858 17 12 17Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 1V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M12 21V23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M1 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M21 12H23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <svg id="darkThemeIcon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <button id="mergeBtn" class="button-footer" type="button" title="Combinar favoritos de uma pasta com outra">
        <img src="../img/icons/duplicate_minus.svg" alt="Mesclar Pastas" style="width:22px;height:22px;display:block;" />
      </button>
      <button id="duplicateBtn" class="button-footer" type="button" title="Duplicar favoritos selecionados">
        <img src="../img/icons/duplicate_plus.svg" alt="Duplicar Favoritos" style="width:22px;height:22px;display:block;" />
      </button>
      <div class="sort-dropdown-container">
        <button id="sortBtn" class="button-footer" type="button" title="Ordenar favoritos">
          <svg fill="currentColor" width="16" height="16" viewBox="0 0 1920 1920" xmlns="http://www.w3.org/2000/svg">
            <path d="M1549.418 299.605V1429.62l203.915-204.032L1920 1392.255l-488.451 488.57-488.57-488.57 166.668-166.667 204.032 204.032V299.605h235.74ZM488.57 160l488.57 488.57-166.67 166.548-204.031-203.914v1129.898h-235.74V611.204L166.668 815.12 0 648.569 488.57 160Z" fill-rule="evenodd"/>
          </svg>
        </button>
        <div id="sortDropdown" class="sort-dropdown">
          <div id="sortByTitle" class="sort-option" title="Ordenar favoritos por título">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 16H4v-2h4V16zm10-9h-4v2h4V7zm-10 6H4v2h4v-2zm6-6h-4v2h4V7zm-6 0H4v2h4V7zm6 3h-4v2h4v-2zM8 7H4v2h4V7zm6 6h-4v2h4v-2zm6 0h-4v2h4v-2zm0 3h-4v2h4v-2zM8 13H4v2h4v-2zm10 0h-4v2h4v-2z"/>
            </svg>
            Por Título
          </div>
          <div id="sortByUrl" class="sort-option" title="Ordenar favoritos por URL">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M11,7H13A2,2 0 0,1 15,9V15A2,2 0 0,1 13,17H11A2,2 0 0,1 9,15V9A2,2 0 0,1 11,7M11,9V15H13V9H11M18,4A2,2 0 0,1 20,6V18A2,2 0 0,1 18,20H6A2,2 0 0,1 4,18V6A2,2 0 0,1 6,4H18M18,6H6V18H18V6Z"/>
            </svg>
            Por URL
          </div>
          <div id="sortByDomain" class="sort-option" title="Ordenar favoritos por domínio do site">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
            </svg>
            Por Domínio
          </div>
          <div id="sortByDateAdded" class="sort-option" title="Ordenar favoritos pela data de criação">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
            </svg>
            Por Data de Criação
          </div>
          <div id="sortByDateModified" class="sort-option" title="Ordenar favoritos pela data de modificação">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            Por Data de Modificação
          </div>
        </div>
      </div>
      <div class="copy-dropdown-container">
        <button id="copyBtn" class="button-footer copy-btn" type="button" title="Copiar informações dos favoritos selecionados">
          <img src="../img/icons/clipboardwv2.svg" alt="Copiar" class="copy-icon">
        </button>
        <div id="copyDropdown" class="copy-dropdown">
          <div id="copyTitles" class="copy-option" title="Copiar somente os títulos dos favoritos selecionados">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M5 4v3h5.5v12h3V7H19V4z"/>
            </svg>
            Copiar Somente Títulos
          </div>
          <div id="copyUrls" class="copy-option" title="Copiar links dos favoritos selecionados">
            <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z"/>
        </svg>
            Copiar Links
          </div>
        </div>
      </div>
      <button id="deleteBtn" class="button-footer delete-btn" type="button" title="Excluir favoritos selecionados">
        <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="si-glyph si-glyph-trash">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g transform="translate(1.000000, 0.000000)" fill="#fff">
              <path d="M0.982,5.073 L2.007,15.339 C2.007,15.705 2.314,16 2.691,16 L10.271,16 C10.648,16 10.955,15.705 10.955,15.339 L11.98,5.073 L0.982,5.073 L0.982,5.073 Z M7.033,14.068 L5.961,14.068 L5.961,6.989 L7.033,6.989 L7.033,14.068 L7.033,14.068 Z M9.033,14.068 L7.961,14.068 L8.961,6.989 L10.033,6.989 L9.033,14.068 L9.033,14.068 Z M5.033,14.068 L3.961,14.068 L2.961,6.989 L4.033,6.989 L5.033,14.068 L5.033,14.068 Z" class="si-glyph-fill"></path>
              <path d="M12.075,2.105 L8.937,2.105 L8.937,0.709 C8.937,0.317 8.481,0 8.081,0 L4.986,0 C4.586,0 4.031,0.225 4.031,0.615 L4.031,2.011 L0.886,2.105 C0.485,2.105 0.159,2.421 0.159,2.813 L0.159,3.968 L12.8,3.968 L12.8,2.813 C12.801,2.422 12.477,2.105 12.075,2.105 L12.075,2.105 Z M4.947,1.44 C4.947,1.128 5.298,0.875 5.73,0.875 L7.294,0.875 C7.726,0.875 8.076,1.129 8.076,1.44 L8.076,2.105 L4.946,2.105 L4.946,1.44 L4.947,1.44 Z" class="si-glyph-fill"></path>
            </g>
          </g>
        </svg>
      </button>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="utils.js"></script>
  <!-- Sistema de Favicons (deve ser carregado antes dos outros) -->
  <script src="favicons/favicon-cache.js"></script>
  <script src="favicons/favicon-loader.js"></script>
  <script src="favicons/favicon-system.js"></script>
  <!-- Apenas sistema de sincronização básico ativado -->
  <script src="bookmark-sync.js"></script>
  <!-- <script src="folder-monitor.js"></script> -->
  <!-- <script src="dynamic-folder-renderer.js"></script> -->
  <!-- <script src="folder-compatibility.js"></script> -->
  <!-- <script src="dynamic-bookmark-renderer.js"></script> -->
  <!-- <script src="bookmark-compatibility.js"></script> -->
  <script src="debug-sync.js"></script>

  <!-- Outros sistemas -->
  <script src="bookmark-element-factory.js"></script>
  <script src="render.js"></script>
  <script src="events.js"></script>
  <script src="selection.js"></script>
  <script src="sortable.js"></script>
  <script src="dragdrop.js"></script>
  <script src="contextmenu.js"></script>
  <script src="popup.js"></script>

  <!-- Verificação de erros (apenas em desenvolvimento) -->
  <script src="error-check.js"></script>

  <!-- Debug de seleção múltipla (apenas em desenvolvimento) -->
  <script src="selection-debug.js"></script>

  <!-- Teste e correção de seleção múltipla (apenas em desenvolvimento) -->
  <script src="selection-fix-test.js"></script>

  <!-- Teste simples de seleção (apenas em desenvolvimento) -->
  <script src="simple-selection-test.js"></script>

  <!-- Correção do drag and drop (apenas em desenvolvimento) -->
  <script src="dragdrop-fix.js"></script>

  <!-- Teste de resolução de conflitos (apenas em desenvolvimento) -->
  <script src="conflict-resolution-test.js"></script>

  <!-- Teste de integração (apenas em desenvolvimento) -->
  <script src="integration-test.js"></script>
</body>
</html>