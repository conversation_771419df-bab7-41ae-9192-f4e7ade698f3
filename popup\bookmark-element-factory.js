/**
 * Factory para criação padronizada de elementos de bookmark
 * Garante estrutura HTML consistente entre render.js e dynamic-bookmark-renderer.js
 */

window.BookmarkElementFactory = window.BookmarkElementFactory || {};

(function() {
    'use strict';

    /**
     * Configuração padrão para elementos de bookmark
     */
    const DEFAULT_CONFIG = {
        useCheckboxContainer: true,
        disableCheckboxPointerEvents: true,
        enableDragDrop: true,
        showRemoveButton: false,
        faviconPlaceholder: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23999' d='M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z'/%3E%3C/svg%3E"
    };

    /**
     * Cria um elemento de bookmark padronizado
     * @param {Object} bookmark - Dados do bookmark
     * @param {string} folderId - ID da pasta pai
     * @param {Object} config - Configurações opcionais
     * @returns {HTMLElement} Elemento do bookmark
     */
    function createBookmarkElement(bookmark, folderId, config = {}) {
        const finalConfig = { ...DEFAULT_CONFIG, ...config };
        
        // Criar elemento principal
        const item = document.createElement(finalConfig.useCheckboxContainer ? "label" : "div");
        item.className = "bookmark-item";
        item.dataset.id = bookmark.id;
        item.dataset.index = bookmark.index || 0;
        item.dataset.folder = folderId;

        // Configurar drag and drop
        if (finalConfig.enableDragDrop) {
            item.setAttribute("draggable", "true");
        }

        // Marcar URLs internas
        if (bookmark.url) {
            const url = bookmark.url.toLowerCase();
            if (url.startsWith('chrome://')) {
                item.dataset.internalUrl = 'chrome';
            } else if (url.startsWith('edge://')) {
                item.dataset.internalUrl = 'edge';
            } else if (url.startsWith('about:')) {
                item.dataset.internalUrl = 'about';
            } else if (url.startsWith('browser://')) {
                item.dataset.internalUrl = 'browser';
            }
        }

        // Criar estrutura do checkbox
        let checkboxContainer;
        if (finalConfig.useCheckboxContainer) {
            checkboxContainer = document.createElement("div");
            checkboxContainer.className = "checkbox-container";
            checkboxContainer.setAttribute("draggable", "false");
        }

        // Criar checkbox
        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.className = "bookmark-checkbox";
        checkbox.value = bookmark.id;
        checkbox.setAttribute("draggable", "false");

        // Configurar pointer events do checkbox
        if (finalConfig.disableCheckboxPointerEvents) {
            checkbox.style.pointerEvents = "none";
        }

        // Verificar se está selecionado
        if (window.UnifiedBookmarkSelection && typeof window.UnifiedBookmarkSelection.getSelectedBookmarkIds === 'function') {
            const selectedIds = window.UnifiedBookmarkSelection.getSelectedBookmarkIds();
            checkbox.checked = selectedIds.includes(bookmark.id);
            if (checkbox.checked) {
                item.classList.add("selected");
            }
        } else if (window.selectedBookmarkIds) {
            // Fallback para sistema antigo
            checkbox.checked = window.selectedBookmarkIds.has(bookmark.id);
            if (checkbox.checked) {
                item.classList.add("selected");
            }
        }

        // Criar container de texto
        const textContainer = document.createElement("div");
        textContainer.className = "text-container";

        // Criar container do título
        const titleContainer = document.createElement("div");
        titleContainer.className = "title-container";

        // Criar favicon
        const favicon = document.createElement("img");
        favicon.className = "favicon";
        favicon.src = finalConfig.faviconPlaceholder;
        favicon.alt = "Favicon";
        favicon.setAttribute("draggable", "false");

        // Criar link do título
        const titleLink = document.createElement("a");
        titleLink.href = bookmark.url;
        titleLink.target = "_blank";
        titleLink.textContent = bookmark.title || bookmark.url;
        titleLink.title = bookmark.title || bookmark.url;
        titleLink.className = "bookmark-link";
        titleLink.setAttribute("draggable", "false");

        // Criar container da URL
        const urlContainer = document.createElement("div");
        urlContainer.className = "url-container";
        urlContainer.textContent = bookmark.url;
        urlContainer.setAttribute("draggable", "false");

        // Montar estrutura do título
        titleContainer.appendChild(favicon);
        titleContainer.appendChild(titleLink);

        // Montar container de texto
        textContainer.appendChild(titleContainer);
        textContainer.appendChild(urlContainer);

        // Criar container de ações (se necessário)
        let actionsContainer;
        if (finalConfig.showRemoveButton) {
            actionsContainer = document.createElement("div");
            actionsContainer.className = "actions";

            const removeBtn = document.createElement("button");
            removeBtn.className = "remove-btn";
            removeBtn.textContent = "×";
            removeBtn.title = "Remover favorito";

            actionsContainer.appendChild(removeBtn);
        }

        // Montar estrutura final
        if (finalConfig.useCheckboxContainer) {
            checkboxContainer.appendChild(checkbox);
            item.appendChild(checkboxContainer);
        } else {
            item.appendChild(checkbox);
        }
        
        item.appendChild(textContainer);
        
        if (actionsContainer) {
            item.appendChild(actionsContainer);
        }

        return item;
    }

    /**
     * Configura eventos padrão para um elemento de bookmark
     * @param {HTMLElement} element - Elemento do bookmark
     * @param {Object} bookmark - Dados do bookmark
     * @param {Object} config - Configurações opcionais
     */
    function setupBookmarkEvents(element, bookmark, config = {}) {
        // Verificar se já foram configurados eventos para evitar duplicação
        if (element.dataset.eventsConfigured === 'true') {
            console.log(`[BookmarkElementFactory] Eventos já configurados para ${bookmark.id}`);
            return;
        }

        const checkbox = element.querySelector('.bookmark-checkbox');
        const removeBtn = element.querySelector('.remove-btn');

        // Configurar evento do checkbox
        if (checkbox) {
            checkbox.addEventListener('change', (e) => {
                console.log(`[BookmarkElementFactory] Checkbox change para ${bookmark.id}: ${checkbox.checked}`);

                // Usar sistema unificado de seleção se disponível
                if (window.UnifiedBookmarkSelection && typeof window.UnifiedBookmarkSelection.updateBookmarkSelection === 'function') {
                    window.UnifiedBookmarkSelection.updateBookmarkSelection(element, checkbox.checked);
                    if (typeof window.UnifiedBookmarkSelection.updateSelectedBookmarksCount === 'function') {
                        window.UnifiedBookmarkSelection.updateSelectedBookmarksCount();
                    }
                    if (typeof window.UnifiedBookmarkSelection.updateLastClickedBookmarkCheckbox === 'function') {
                        window.UnifiedBookmarkSelection.updateLastClickedBookmarkCheckbox(checkbox);
                    }
                } else {
                    // Fallback para sistema antigo
                    if (typeof updateBookmarkSelection === 'function') {
                        updateBookmarkSelection(element, checkbox.checked);
                    }
                    if (typeof updateSelectedBookmarksCount === 'function') {
                        updateSelectedBookmarksCount();
                    }
                    if (typeof updateLastClickedBookmarkCheckbox === 'function') {
                        updateLastClickedBookmarkCheckbox(checkbox);
                    }
                }
            });
        }

        // Configurar evento do botão de remoção
        if (removeBtn) {
            removeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (confirm('Deseja remover este favorito?')) {
                    chrome.bookmarks.remove(bookmark.id, () => {
                        if (chrome.runtime.lastError) {
                            console.error('Erro ao remover bookmark:', chrome.runtime.lastError);
                            if (typeof showActionFeedback === 'function') {
                                showActionFeedback('Erro ao remover favorito', 'error');
                            }
                        } else {
                            if (typeof showActionFeedback === 'function') {
                                showActionFeedback('Favorito removido', 'success');
                            }
                        }
                    });
                }
            });
        }

        // Configurar clique no item (apenas se checkbox não for clicável)
        if (config.disableCheckboxPointerEvents !== false) {
            element.addEventListener('click', (e) => {
                // Evitar duplo toggle se clicou diretamente no checkbox ou botão
                if (e.target === checkbox || e.target === removeBtn) return;

                // Permitir que links sejam abertos normalmente
                if (e.target.tagName === 'A') return;

                // Toggle do checkbox
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });
        }

        // Marcar como configurado
        element.dataset.eventsConfigured = 'true';
    }

    /**
     * Carrega favicon para um elemento
     * @param {HTMLElement} element - Elemento do bookmark
     * @param {string} url - URL do bookmark
     */
    function loadFavicon(element, url) {
        const favicon = element.querySelector('.favicon');
        if (favicon && window.FaviconSystem && window.FaviconSystem.loadFavicon) {
            window.FaviconSystem.loadFavicon(favicon, url);
        }
    }

    /**
     * Atualiza um elemento de bookmark existente
     * @param {HTMLElement} element - Elemento existente
     * @param {Object} bookmark - Novos dados do bookmark
     */
    function updateBookmarkElement(element, bookmark) {
        // Atualizar dataset
        element.dataset.id = bookmark.id;
        element.dataset.index = bookmark.index || 0;
        element.dataset.folder = bookmark.folderId || bookmark.parentId;

        // Atualizar título e URL
        const titleLink = element.querySelector('.bookmark-link');
        if (titleLink) {
            titleLink.textContent = bookmark.title || bookmark.url;
            titleLink.href = bookmark.url;
            titleLink.title = bookmark.title || bookmark.url;
        }

        const urlContainer = element.querySelector('.url-container');
        if (urlContainer) {
            urlContainer.textContent = bookmark.url;
        }

        // Atualizar checkbox
        const checkbox = element.querySelector('.bookmark-checkbox');
        if (checkbox) {
            checkbox.value = bookmark.id;

            // Verificar se deve estar selecionado
            if (window.UnifiedBookmarkSelection && typeof window.UnifiedBookmarkSelection.getSelectedBookmarkIds === 'function') {
                const selectedIds = window.UnifiedBookmarkSelection.getSelectedBookmarkIds();
                checkbox.checked = selectedIds.includes(bookmark.id);

                if (checkbox.checked) {
                    element.classList.add("selected");
                } else {
                    element.classList.remove("selected");
                }
            } else if (window.selectedBookmarkIds) {
                // Fallback para sistema antigo
                checkbox.checked = window.selectedBookmarkIds.has(bookmark.id);

                if (checkbox.checked) {
                    element.classList.add("selected");
                } else {
                    element.classList.remove("selected");
                }
            }
        }

        // Recarregar favicon
        loadFavicon(element, bookmark.url);
    }

    // API pública
    window.BookmarkElementFactory = {
        createBookmarkElement,
        setupBookmarkEvents,
        loadFavicon,
        updateBookmarkElement,
        DEFAULT_CONFIG
    };

    console.log('[BookmarkElementFactory] Sistema carregado');

})();
