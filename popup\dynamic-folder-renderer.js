/**
 * Sistema de Renderização Dinâmica de Pastas
 * 
 * Renderiza a primeira coluna de forma reativa, refletindo mudanças
 * em tempo real tanto por ações de background quanto por uso da extensão.
 */

window.DynamicFolderRenderer = window.DynamicFolderRenderer || {};

(function() {
    'use strict';

    // Estado do sistema
    let isInitialized = false;
    let currentFolderTree = new Map();
    let folderElements = new Map(); // Cache de elementos DOM
    let selectedFolders = new Set();
    let containerElement = null;

    // Configurações
    const CONFIG = {
        ENABLE_LOGS: true,
        ANIMATION_DURATION: 200,
        DEBOUNCE_DELAY: 50, // Reduzido para atualizações mais rápidas
        RENAME_DEBOUNCE_DELAY: 10 // Delay mínimo para renomeações
    };

    /**
     * Inicializa o sistema de renderização dinâmica
     * @param {HTMLElement} container - Container das pastas
     */
    function init(container) {
        if (isInitialized) return;

        containerElement = container;
        console.log('[DynamicFolderRenderer] Inicializando sistema...');

        // Carregar estado inicial
        loadInitialState();

        // Configurar listeners para mudanças
        setupBookmarkListeners();

        isInitialized = true;
        console.log('[DynamicFolderRenderer] Sistema inicializado');
    }

    /**
     * Carrega o estado inicial das pastas
     */
    function loadInitialState() {
        chrome.bookmarks.getTree((tree) => {
            if (chrome.runtime.lastError) {
                console.error('[DynamicFolderRenderer] Erro ao carregar árvore:', chrome.runtime.lastError);
                return;
            }

            // Processar árvore e renderizar
            processAndRender(tree[0].children);
        });
    }

    /**
     * Configura listeners para mudanças nos bookmarks
     */
    function setupBookmarkListeners() {
        // Listener para remoção
        chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
            handleFolderRemoved(id, removeInfo);
        });

        // Listener para criação
        chrome.bookmarks.onCreated.addListener((id, bookmark) => {
            handleFolderCreated(id, bookmark);
        });

        // Listener para alteração
        chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
            handleFolderChanged(id, changeInfo);
        });

        // Listener para movimentação
        chrome.bookmarks.onMoved.addListener((id, moveInfo) => {
            handleFolderMoved(id, moveInfo);
        });

        console.log('[DynamicFolderRenderer] Listeners configurados');
    }

    /**
     * Processa árvore de pastas e renderiza
     * @param {Array} folders - Array de pastas
     */
    function processAndRender(folders) {
        // Limpar estado anterior
        currentFolderTree.clear();
        folderElements.clear();

        // Processar árvore
        processFolderTree(folders, null);

        // Renderizar interface
        renderFolderTree();

        // Atualizar contador
        updateFolderCount();
    }

    /**
     * Processa árvore de pastas recursivamente
     * @param {Array} folders - Array de pastas
     * @param {string} parentId - ID da pasta pai
     */
    function processFolderTree(folders, parentId) {
        if (!folders) return;

        folders.forEach(folder => {
            if (!folder.url) { // É uma pasta
                const folderData = {
                    id: folder.id,
                    title: folder.title,
                    parentId: parentId,
                    index: folder.index,
                    children: [],
                    isSelected: selectedFolders.has(folder.id)
                };

                currentFolderTree.set(folder.id, folderData);

                // Processar subpastas
                if (folder.children) {
                    processFolderTree(folder.children, folder.id);
                    
                    // Adicionar IDs dos filhos
                    folder.children.forEach(child => {
                        if (!child.url) {
                            folderData.children.push(child.id);
                        }
                    });
                }
            }
        });
    }

    /**
     * Renderiza a árvore de pastas na interface
     */
    function renderFolderTree() {
        if (!containerElement) return;

        // Criar fragment para melhor performance
        const fragment = document.createDocumentFragment();

        // Renderizar pastas raiz
        const rootFolders = Array.from(currentFolderTree.values())
            .filter(folder => !folder.parentId)
            .sort((a, b) => (a.index || 0) - (b.index || 0));

        rootFolders.forEach(folder => {
            const element = createFolderElement(folder);
            fragment.appendChild(element);
            
            // Renderizar subpastas recursivamente
            renderSubfolders(folder, element, fragment);
        });

        // Atualizar DOM de uma vez
        containerElement.innerHTML = '';
        containerElement.appendChild(fragment);

        if (CONFIG.ENABLE_LOGS) {
            console.log(`[DynamicFolderRenderer] Renderizadas ${currentFolderTree.size} pastas`);
        }
    }

    /**
     * Renderiza subpastas recursivamente
     * @param {Object} parentFolder - Pasta pai
     * @param {HTMLElement} parentElement - Elemento pai
     * @param {DocumentFragment} fragment - Fragment principal
     */
    function renderSubfolders(parentFolder, parentElement, fragment) {
        if (!parentFolder.children || parentFolder.children.length === 0) return;

        const subContainer = document.createElement("div");
        subContainer.className = "folder-nest";
        subContainer.dataset.parentId = parentFolder.id;

        parentFolder.children
            .map(childId => currentFolderTree.get(childId))
            .filter(child => child)
            .sort((a, b) => (a.index || 0) - (b.index || 0))
            .forEach(childFolder => {
                const childElement = createFolderElement(childFolder);
                subContainer.appendChild(childElement);
                
                // Recursão para subpastas
                renderSubfolders(childFolder, childElement, subContainer);
            });

        fragment.appendChild(subContainer);
    }

    /**
     * Cria elemento DOM para uma pasta
     * @param {Object} folder - Dados da pasta
     * @returns {HTMLElement} Elemento da pasta
     */
    function createFolderElement(folder) {
        const label = document.createElement("label");
        label.className = "folder-option";
        label.dataset.folderId = folder.id;
        label.dataset.parentId = folder.parentId || '0';
        label.dataset.index = folder.index || 0;
        label.setAttribute("draggable", "true");

        // Checkbox
        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.value = folder.id;
        checkbox.className = "folder-checkbox";
        checkbox.style.pointerEvents = "none";
        checkbox.checked = folder.isSelected;

        // Container do título
        const titleContainer = document.createElement("div");
        titleContainer.className = "folder-title-container";

        const titleSpan = document.createElement("span");
        titleSpan.className = "folder-title";
        titleSpan.textContent = folder.title || "(Sem nome)";

        titleContainer.appendChild(titleSpan);

        // Montar elemento
        label.appendChild(checkbox);
        label.appendChild(titleContainer);

        // Configurar eventos
        setupFolderEvents(label, folder);

        // Armazenar no cache
        folderElements.set(folder.id, label);

        return label;
    }

    /**
     * Configura eventos para um elemento de pasta
     * @param {HTMLElement} element - Elemento da pasta
     * @param {Object} folder - Dados da pasta
     */
    function setupFolderEvents(element, folder) {
        const checkbox = element.querySelector('.folder-checkbox');

        // Evento de clique na pasta
        element.addEventListener('click', (e) => {
            e.preventDefault();
            toggleFolderSelection(folder.id, element, checkbox);
        });

        // Eventos de drag and drop
        setupDragEvents(element, folder);

        // Eventos de contexto
        setupContextEvents(element, folder);
    }

    /**
     * Alterna seleção de uma pasta
     * @param {string} folderId - ID da pasta
     * @param {HTMLElement} element - Elemento da pasta
     * @param {HTMLInputElement} checkbox - Checkbox da pasta
     */
    function toggleFolderSelection(folderId, element, checkbox) {
        const wasSelected = selectedFolders.has(folderId);
        
        if (wasSelected) {
            selectedFolders.delete(folderId);
            checkbox.checked = false;
            element.classList.remove('selected');
        } else {
            selectedFolders.add(folderId);
            checkbox.checked = true;
            element.classList.add('selected');
        }

        // Atualizar estado global
        if (typeof selectedFolderIds !== 'undefined') {
            if (wasSelected) {
                selectedFolderIds.delete(folderId);
            } else {
                selectedFolderIds.add(folderId);
            }
        }

        // Atualizar contador
        updateSelectedFoldersCount();

        // Carregar/descarregar favoritos
        if (wasSelected) {
            unloadFolderBookmarks(folderId);
        } else {
            loadFolderBookmarks(folderId);
        }
    }

    /**
     * Carrega favoritos de uma pasta
     * @param {string} folderId - ID da pasta
     */
    function loadFolderBookmarks(folderId) {
        if (typeof blockRendering !== 'undefined' && blockRendering) return;

        // Usar sistema dinâmico de bookmarks se disponível
        if (window.DynamicBookmarkRenderer) {
            const currentFolders = new Set(selectedFolders);
            currentFolders.add(folderId);
            window.DynamicBookmarkRenderer.loadBookmarksFromFolders(currentFolders);
        } else {
            // Fallback para sistema antigo
            chrome.bookmarks.getChildren(folderId, (children) => {
                if (chrome.runtime.lastError) {
                    console.error('[DynamicFolderRenderer] Erro ao carregar favoritos:', chrome.runtime.lastError);
                    return;
                }

                const items = children
                    .filter(c => c.url)
                    .sort((a, b) => (a.index || 0) - (b.index || 0));

                // Renderizar favoritos
                if (typeof renderBookmarks === 'function') {
                    const container = document.getElementById('bookmarksContainer');
                    if (container) {
                        renderBookmarks(items, container, false, folderId, false);
                    }
                }
            });
        }
    }

    /**
     * Remove favoritos de uma pasta da interface
     * @param {string} folderId - ID da pasta
     */
    function unloadFolderBookmarks(folderId) {
        // Usar sistema dinâmico de bookmarks se disponível
        if (window.DynamicBookmarkRenderer) {
            const remainingFolders = new Set(selectedFolders);
            remainingFolders.delete(folderId);
            window.DynamicBookmarkRenderer.loadBookmarksFromFolders(remainingFolders);
        } else {
            // Fallback para sistema antigo
            const container = document.getElementById('bookmarksContainer');
            if (container) {
                const elementsToRemove = container.querySelectorAll(`[data-folder="${folderId}"]`);
                elementsToRemove.forEach(el => el.remove());
            }

            // Limpar do mapa de favoritos
            if (typeof folderBookmarkMap !== 'undefined') {
                folderBookmarkMap.delete(folderId);
            }

            // Atualizar contador
            if (typeof updateBookmarkCount === 'function') {
                updateBookmarkCount();
            }
        }
    }

    /**
     * Configura eventos de drag and drop
     * @param {HTMLElement} element - Elemento da pasta
     * @param {Object} folder - Dados da pasta
     */
    function setupDragEvents(element, folder) {
        // Implementar drag and drop se necessário
        // Por enquanto, manter compatibilidade com sistema existente
    }

    /**
     * Configura eventos de contexto
     * @param {HTMLElement} element - Elemento da pasta
     * @param {Object} folder - Dados da pasta
     */
    function setupContextEvents(element, folder) {
        // Implementar menu de contexto se necessário
        // Por enquanto, manter compatibilidade com sistema existente
    }

    /**
     * Trata remoção de pasta
     * @param {string} id - ID da pasta removida
     * @param {Object} removeInfo - Informações da remoção
     */
    function handleFolderRemoved(id, removeInfo) {
        if (!removeInfo.node || removeInfo.node.url) return; // Não é uma pasta

        console.log(`[DynamicFolderRenderer] Pasta removida: ${id}`);

        // Remover do estado
        currentFolderTree.delete(id);
        selectedFolders.delete(id);

        // Remover elemento da interface
        const element = folderElements.get(id);
        if (element) {
            // Animação de remoção
            element.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease-out`;
            element.style.opacity = '0';
            
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
                folderElements.delete(id);
            }, CONFIG.ANIMATION_DURATION);
        }

        // Remover subcontainer se existir
        const subContainer = containerElement.querySelector(`[data-parent-id="${id}"]`);
        if (subContainer) {
            subContainer.remove();
        }

        // Atualizar contadores
        updateFolderCount();
        updateSelectedFoldersCount();

        // Remover favoritos da interface
        unloadFolderBookmarks(id);
    }

    /**
     * Trata criação de pasta
     * @param {string} id - ID da pasta criada
     * @param {Object} bookmark - Dados da pasta
     */
    function handleFolderCreated(id, bookmark) {
        if (bookmark.url) return; // Não é uma pasta

        console.log(`[DynamicFolderRenderer] Pasta criada: ${id}`);

        // Recarregar árvore completa para manter estrutura
        debounceReload();
    }

    /**
     * Trata alteração de pasta com atualização imediata
     * @param {string} id - ID da pasta alterada
     * @param {Object} changeInfo - Informações da alteração
     */
    function handleFolderChanged(id, changeInfo) {
        const folder = currentFolderTree.get(id);
        if (!folder) return;

        console.log(`[DynamicFolderRenderer] Pasta alterada: ${id}`);

        // Atualizar dados
        if (changeInfo.title !== undefined) {
            folder.title = changeInfo.title;
        }

        // Atualizar elemento na interface IMEDIATAMENTE
        const element = folderElements.get(id);
        if (element) {
            const titleSpan = element.querySelector('.folder-title');
            if (titleSpan) {
                titleSpan.textContent = folder.title || "(Sem nome)";
                console.log(`[DynamicFolderRenderer] Nome atualizado imediatamente: ${folder.title}`);
            }
        }

        // Para renomeações, não usar debounce
        if (changeInfo.title !== undefined) {
            // Atualização imediata sem debounce para renomeações
            return;
        }
    }

    /**
     * Trata renomeação de pasta com prioridade máxima
     * @param {string} id - ID da pasta renomeada
     * @param {Object} newData - Novos dados
     * @param {Object} oldData - Dados antigos
     */
    function handleFolderRenamed(id, newData, oldData) {
        console.log(`[DynamicFolderRenderer] Pasta renomeada: ${id} "${oldData.title}" → "${newData.title}"`);

        // Atualizar imediatamente sem debounce
        handleFolderChanged(id, { title: newData.title });
    }

    /**
     * Trata movimentação de pasta
     * @param {string} id - ID da pasta movida
     * @param {Object} moveInfo - Informações da movimentação
     */
    function handleFolderMoved(id, moveInfo) {
        console.log(`[DynamicFolderRenderer] Pasta movida: ${id}`);

        // Recarregar árvore completa para manter estrutura
        debounceReload();
    }

    /**
     * Recarregamento com debounce para evitar múltiplas atualizações
     */
    let reloadTimeout;
    function debounceReload() {
        clearTimeout(reloadTimeout);
        reloadTimeout = setTimeout(() => {
            loadInitialState();
        }, CONFIG.DEBOUNCE_DELAY);
    }

    /**
     * Atualiza contador de pastas
     */
    function updateFolderCount() {
        const countElement = document.getElementById('folderCount');
        if (countElement) {
            countElement.textContent = `Pastas exibidas: ${currentFolderTree.size}`;
        }
    }

    /**
     * Atualiza contador de pastas selecionadas
     */
    function updateSelectedFoldersCount() {
        const countElement = document.getElementById('selectedFoldersCount');
        if (countElement) {
            countElement.textContent = `Selecionadas: ${selectedFolders.size}`;
            
            if (selectedFolders.size > 0) {
                countElement.classList.add('has-selected');
            } else {
                countElement.classList.remove('has-selected');
            }
        }
    }

    /**
     * Força recarregamento completo
     */
    function forceReload() {
        console.log('[DynamicFolderRenderer] Forçando recarregamento...');
        loadInitialState();
    }

    /**
     * Obtém pastas selecionadas (compatibilidade)
     * @returns {Set} Set de IDs das pastas selecionadas
     */
    function getSelectedFolders() {
        return new Set(selectedFolders);
    }

    /**
     * Define pastas selecionadas (compatibilidade)
     * @param {Set|Array} folders - Pastas para selecionar
     */
    function setSelectedFolders(folders) {
        selectedFolders.clear();

        const folderArray = Array.isArray(folders) ? folders : Array.from(folders);
        folderArray.forEach(folderId => {
            selectedFolders.add(folderId);

            // Atualizar elemento visual
            const element = folderElements.get(folderId);
            if (element) {
                const checkbox = element.querySelector('.folder-checkbox');
                if (checkbox) {
                    checkbox.checked = true;
                    element.classList.add('selected');
                }
            }
        });

        updateSelectedFoldersCount();
    }

    /**
     * Obtém estatísticas do sistema
     */
    function getStats() {
        return {
            initialized: isInitialized,
            totalFolders: currentFolderTree.size,
            selectedFolders: selectedFolders.size,
            cachedElements: folderElements.size
        };
    }

    // API pública
    window.DynamicFolderRenderer = {
        init,
        forceReload,
        getStats,
        getSelectedFolders,
        setSelectedFolders,
        handleFolderChanged,
        handleFolderRenamed, // Nova função específica para renomeações
        handleFolderRemoved,
        handleFolderCreated,
        handleFolderMoved
    };

    // Comandos globais para debug
    window.reloadFolders = () => forceReload();
    window.folderRendererStats = () => getStats();

    console.log('[DynamicFolderRenderer] Sistema carregado');

})();
