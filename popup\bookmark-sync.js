/**
 * Sistema de Sincronização de Bookmarks
 * 
 * Gerencia a sincronização da interface quando bookmarks são alterados
 * externamente (pelo navegador ou outras extensões).
 */

window.BookmarkSync = window.BookmarkSync || {};

(function() {
    'use strict';

    // Estado do sistema
    let isInitialized = false;
    let updateQueue = [];
    let isProcessingQueue = false;
    let isInitializing = true; // Flag para evitar mensagens durante inicialização

    /**
     * Inicializa o sistema de sincronização
     */
    function init() {
        if (isInitialized) return;

        console.log('[BookmarkSync] Inicializando sistema de sincronização...');

        // Listener para mensagens do background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('[BookmarkSync] Mensagem recebida:', message);
            if (message.type === 'BOOKMARK_CHANGE') {
                console.log('[BookmarkSync] Processando mudança de bookmark:', message.changeType, message.data);
                handleBookmarkChange(message.changeType, message.data);

                // Responder para confirmar recebimento
                if (sendResponse) {
                    sendResponse({ received: true, timestamp: Date.now() });
                }
            }
            return true; // Manter canal aberto para resposta assíncrona
        });

        // Configurar listeners diretos dos bookmarks como fallback
        setupDirectBookmarkListeners();

        isInitialized = true;
        console.log('[BookmarkSync] Sistema de sincronização inicializado');

        // Marcar fim da inicialização após um pequeno delay para permitir carregamento inicial
        setTimeout(() => {
            isInitializing = false;
            console.log('[BookmarkSync] Período de inicialização finalizado - mensagens de detecção ativadas');
        }, 2000); // 2 segundos de delay
    }

    /**
     * Configura listeners diretos como fallback
     */
    function setupDirectBookmarkListeners() {
        console.log('[BookmarkSync] Configurando listeners diretos como fallback...');

        // Listener direto para mudanças
        chrome.bookmarks.onChanged.addListener((id, changeInfo) => {
            console.log('[BookmarkSync] Mudança detectada diretamente:', id, changeInfo);
            handleBookmarkChange('changed', { id, ...changeInfo });
        });

        // Listener direto para remoções
        chrome.bookmarks.onRemoved.addListener((id, removeInfo) => {
            console.log('[BookmarkSync] Remoção detectada diretamente:', id, removeInfo);
            handleBookmarkChange('removed', { id, ...removeInfo });
        });

        // Listener direto para criações
        chrome.bookmarks.onCreated.addListener((id, bookmark) => {
            console.log('[BookmarkSync] Criação detectada diretamente:', id, bookmark);
            handleBookmarkChange('created', { id, bookmark });
        });

        // Listener direto para movimentações
        chrome.bookmarks.onMoved.addListener((id, moveInfo) => {
            console.log('[BookmarkSync] Movimentação detectada diretamente:', id, moveInfo);
            handleBookmarkChange('moved', { id, ...moveInfo });
        });
    }

    /**
     * Processa mudanças nos bookmarks
     * @param {string} changeType - Tipo de mudança
     * @param {Object} data - Dados da mudança
     */
    function handleBookmarkChange(changeType, data) {
        console.log(`[BookmarkSync] Mudança detectada: ${changeType}`, data);

        // Adicionar à fila de atualizações
        updateQueue.push({ changeType, data, timestamp: Date.now() });

        // Processar fila
        processUpdateQueue();
    }

    /**
     * Processa a fila de atualizações
     */
    async function processUpdateQueue() {
        if (isProcessingQueue || updateQueue.length === 0) return;

        isProcessingQueue = true;

        try {
            while (updateQueue.length > 0) {
                const update = updateQueue.shift();
                await processUpdate(update);
                
                // Pequeno delay para não sobrecarregar a interface
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } catch (error) {
            console.error('[BookmarkSync] Erro ao processar fila de atualizações:', error);
        } finally {
            isProcessingQueue = false;
        }
    }

    /**
     * Processa uma atualização individual
     * @param {Object} update - Atualização para processar
     */
    async function processUpdate(update) {
        const { changeType, data } = update;

        switch (changeType) {
            case 'removed':
                await handleRemoved(data);
                break;
            case 'created':
                await handleCreated(data);
                break;
            case 'changed':
                await handleChanged(data);
                break;
            case 'moved':
                await handleMoved(data);
                break;
            default:
                console.warn(`[BookmarkSync] Tipo de mudança desconhecido: ${changeType}`);
        }
    }

    /**
     * Trata remoção de bookmark/pasta
     * @param {Object} data - Dados da remoção
     */
    async function handleRemoved(data) {
        const { id, node } = data;
        
        console.log(`[BookmarkSync] Processando remoção: ${id}`);

        // Se foi uma pasta que foi removida
        if (node && !node.url) {
            await handleFolderRemoved(id);
        } else {
            // Se foi um bookmark que foi removido
            await handleBookmarkRemoved(id);
        }
    }

    /**
     * Trata remoção de pasta
     * @param {string} folderId - ID da pasta removida
     */
    async function handleFolderRemoved(folderId) {
        console.log(`[BookmarkSync] Pasta removida: ${folderId}`);

        // Remover da lista de pastas selecionadas se estiver lá
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(folderId)) {
            selectedFolderIds.delete(folderId);
            
            // Atualizar contador
            const selectedCountEl = document.getElementById("selectedFoldersCount");
            if (selectedCountEl) {
                selectedCountEl.textContent = `Selecionadas: ${selectedFolderIds.size}`;
                if (selectedFolderIds.size === 0) {
                    selectedCountEl.classList.remove("has-selected");
                }
            }
        }

        // Remover elemento da interface
        const folderElement = document.querySelector(`input[value="${folderId}"]`);
        if (folderElement) {
            const folderOption = folderElement.closest('.folder-option');
            if (folderOption) {
                folderOption.remove();
                console.log(`[BookmarkSync] Elemento da pasta ${folderId} removido da interface`);
            }
        }

        // Remover favoritos desta pasta da coluna 2
        const bookmarkElements = document.querySelectorAll(`[data-folder="${folderId}"]`);
        bookmarkElements.forEach(element => element.remove());

        // Limpar cache relacionado
        if (typeof folderBookmarkMap !== 'undefined') {
            folderBookmarkMap.delete(folderId);
        }

        // Atualizar contadores
        updateFolderCount();
        updateBookmarkCount();

        showActionFeedback('Pasta removida', 'info');
    }

    /**
     * Trata remoção de bookmark
     * @param {string} bookmarkId - ID do bookmark removido
     */
    async function handleBookmarkRemoved(bookmarkId) {
        console.log(`[BookmarkSync] Bookmark removido: ${bookmarkId}`);

        // Remover elemento da interface
        const bookmarkElement = document.querySelector(`[data-id="${bookmarkId}"]`);
        if (bookmarkElement) {
            bookmarkElement.remove();
            console.log(`[BookmarkSync] Elemento do bookmark ${bookmarkId} removido da interface`);
        }

        // Remover do cache se disponível
        if (typeof window.bookmarkElementCache !== 'undefined' && window.bookmarkElementCache) {
            window.bookmarkElementCache.delete(bookmarkId);
        }

        // Remover da seleção se estiver selecionado
        if (typeof selectedBookmarkIds !== 'undefined') {
            selectedBookmarkIds.delete(bookmarkId);
        }

        // Atualizar contadores
        updateBookmarkCount();
        if (typeof updateSelectedBookmarksCount === 'function') {
            updateSelectedBookmarksCount();
        }
    }

    /**
     * Trata criação de bookmark/pasta
     * @param {Object} data - Dados da criação
     */
    async function handleCreated(data) {
        const { bookmark } = data;

        console.log(`[BookmarkSync] Item criado: ${bookmark.id}`);

        // Se foi uma pasta criada, recarregar lista de pastas
        if (!bookmark.url) {
            await reloadFolderList();
            // Só mostrar mensagem se não estiver inicializando
            if (!isInitializing) {
                showActionFeedback('Nova pasta detectada', 'info');
            }
        } else {
            // Se foi um bookmark criado, verificar se está em pasta selecionada
            if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.has(bookmark.parentId)) {
                await reloadSelectedFolders();
                // Só mostrar mensagem se não estiver inicializando
                if (!isInitializing) {
                    showActionFeedback('Novo bookmark detectado', 'info');
                }
            }
        }
    }

    /**
     * Trata alteração de bookmark/pasta com prioridade para renomeações
     * @param {Object} data - Dados da alteração
     */
    async function handleChanged(data) {
        const { id, title, url } = data;

        console.log(`[BookmarkSync] Item alterado: ${id}`);

        // Se foi uma pasta alterada (renomeação)
        if (!url && title) {
            console.log(`[BookmarkSync] Processando renomeação de pasta: ${id} → "${title}"`);

            // Usar sistema dinâmico de pastas se disponível
            if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.handleFolderRenamed === 'function') {
                window.DynamicFolderRenderer.handleFolderRenamed(id, { title });
            } else if (window.FolderMonitor && typeof window.FolderMonitor.processRenameImmediately === 'function') {
                window.FolderMonitor.processRenameImmediately(id, title, '');
            } else {
                // Fallback: recarregar lista completa
                await reloadFolderList();
            }

            // Atualizar título da pasta na interface (fallback)
            const folderCheckbox = document.querySelector(`input[value="${id}"]`);
            if (folderCheckbox) {
                const label = folderCheckbox.parentElement;
                if (label) {
                    // Procurar por .folder-title primeiro
                    const titleSpan = label.querySelector('.folder-title');
                    if (titleSpan) {
                        titleSpan.textContent = title;
                        console.log(`[BookmarkSync] Nome atualizado via .folder-title: ${title}`);
                    } else {
                        // Fallback: manter apenas o checkbox e atualizar o texto
                        const textNode = Array.from(label.childNodes).find(node => node.nodeType === Node.TEXT_NODE);
                        if (textNode) {
                            textNode.textContent = title;
                            console.log(`[BookmarkSync] Nome atualizado via text node: ${title}`);
                        }
                    }
                }
            }

            // Notificar DynamicFolderRenderer se disponível
            if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.handleFolderRenamed === 'function') {
                window.DynamicFolderRenderer.handleFolderRenamed(id, { title }, { title: 'old_title' });
            }

            // Só mostrar mensagem se não estiver inicializando
            if (!isInitializing) {
                showActionFeedback('Pasta renomeada', 'info');
            }
        } else {
            // Se foi um bookmark alterado
            const bookmarkElement = document.querySelector(`[data-id="${id}"]`);
            if (bookmarkElement) {
                // Atualizar título e URL
                const titleElement = bookmarkElement.querySelector('.bookmark-link');
                const urlElement = bookmarkElement.querySelector('.url-container');
                
                if (titleElement) titleElement.textContent = title;
                if (urlElement) urlElement.textContent = url;
                
                // Recarregar favicon se a URL mudou
                if (url) {
                    const favicon = bookmarkElement.querySelector('.favicon');
                    if (favicon && window.FaviconSystem?.loadFavicon) {
                        window.FaviconSystem.loadFavicon(favicon, url);
                    }
                }
            }
            // Só mostrar mensagem se não estiver inicializando
            if (!isInitializing) {
                showActionFeedback('Bookmark alterado', 'info');
            }
        }
    }

    /**
     * Trata movimentação de bookmark/pasta
     * @param {Object} data - Dados da movimentação
     */
    async function handleMoved(data) {
        console.log(`[BookmarkSync] Item movido: ${data.id}`);

        // Para movimentações, é mais seguro recarregar tudo
        await reloadFolderList();
        
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.size > 0) {
            await reloadSelectedFolders();
        }

        // Só mostrar mensagem se não estiver inicializando
        if (!isInitializing) {
            showActionFeedback('Item movido', 'info');
        }
    }

    /**
     * Recarrega a lista de pastas
     */
    async function reloadFolderList() {
        console.log('[BookmarkSync] Recarregando lista de pastas...');

        // Usar sistema dinâmico de pastas se disponível
        if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.forceReload === 'function') {
            window.DynamicFolderRenderer.forceReload();
        } else if (typeof window.loadBookmarkFolders === 'function') {
            window.loadBookmarkFolders();
        } else if (typeof loadBookmarkFolders === 'function') {
            loadBookmarkFolders();
        } else {
            // Fallback: recarregar usando API diretamente
            chrome.bookmarks.getTree((tree) => {
                const roots = tree[0].children;
                const container = document.getElementById("folderCheckboxes");
                if (typeof populateFolderCheckboxes === 'function') {
                    populateFolderCheckboxes(roots, container);
                }
                if (typeof updateFolderCount === 'function') {
                    updateFolderCount();
                }
            });
        }
    }

    /**
     * Recarrega favoritos das pastas selecionadas
     */
    async function reloadSelectedFolders() {
        // Usar sistema dinâmico de bookmarks se disponível
        if (window.DynamicBookmarkRenderer && typeof window.DynamicBookmarkRenderer.loadBookmarksFromFolders === 'function') {
            const selectedFolders = window.DynamicFolderRenderer ?
                window.DynamicFolderRenderer.getSelectedFolders() :
                (typeof selectedFolderIds !== 'undefined' ? selectedFolderIds : new Set());

            window.DynamicBookmarkRenderer.loadBookmarksFromFolders(selectedFolders);
        } else if (typeof window.reloadSelectedFolders === 'function') {
            window.reloadSelectedFolders(true); // Preservar scroll
        } else {
            // Fallback: recarregar manualmente
            if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.size > 0) {
                const folderArray = Array.from(selectedFolderIds);
                for (const folderId of folderArray) {
                    if (typeof updateFolderContents === 'function') {
                        updateFolderContents(folderId, true);
                    }
                }
            }
        }
    }

    /**
     * Força uma sincronização completa
     */
    async function forceSync() {
        console.log('[BookmarkSync] Forçando sincronização completa...');
        
        await reloadFolderList();
        
        if (typeof selectedFolderIds !== 'undefined' && selectedFolderIds.size > 0) {
            await reloadSelectedFolders();
        }
        
        showActionFeedback('Sincronização completa realizada', 'success');
    }

    /**
     * Marca o fim do período de inicialização
     */
    function finishInitialization() {
        isInitializing = false;
        console.log('[BookmarkSync] Inicialização marcada como finalizada manualmente');
    }

    /**
     * Obtém estatísticas do sistema
     */
    function getStats() {
        return {
            initialized: isInitialized,
            initializing: isInitializing,
            queueSize: updateQueue.length,
            processing: isProcessingQueue
        };
    }

    // API pública
    window.BookmarkSync = {
        init,
        forceSync,
        finishInitialization,
        getStats
    };

    // TEMPORARIAMENTE DESABILITADO - Auto-inicializar quando o DOM estiver pronto
    // CAUSA STACK OVERFLOW - PRECISA INVESTIGAR
    /*
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        setTimeout(init, 0);
    }
    */

    // Inicialização manual disponível
    window.BookmarkSync.init = init;

    // Comando global para debug
    window.forceBookmarkSync = () => forceSync();

    console.log('[BookmarkSync] Sistema carregado');

})();
