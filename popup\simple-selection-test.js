/**
 * Teste Simples de Seleção
 * Testa apenas as funcionalidades básicas sem complexidade
 */

window.SimpleSelectionTest = window.SimpleSelectionTest || {};

(function() {
    'use strict';

    /**
     * Teste básico de funcionamento
     */
    function basicTest() {
        console.log('🧪 [SimpleSelectionTest] Iniciando teste básico...');
        
        // 1. Verificar se elementos existem
        const selectBtn = document.getElementById("selectAllBookmarksBtn");
        const deselectBtn = document.getElementById("deselectAllBookmarksBtn");
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        
        console.log(`📊 [SimpleSelectionTest] Elementos encontrados:`);
        console.log(`   - Botão Selecionar: ${!!selectBtn}`);
        console.log(`   - Botão Desselecionar: ${!!deselectBtn}`);
        console.log(`   - Checkboxes: ${checkboxes.length}`);
        
        // 2. Verificar sistema unificado
        console.log(`📊 [SimpleSelectionTest] Sistema unificado:`);
        console.log(`   - UnifiedBookmarkSelection: ${!!window.UnifiedBookmarkSelection}`);
        console.log(`   - selectedBookmarkIds: ${!!window.selectedBookmarkIds}`);
        
        if (window.selectedBookmarkIds) {
            console.log(`   - selectedBookmarkIds.size: ${window.selectedBookmarkIds.size}`);
        }
        
        // 3. Testar seleção manual de um checkbox
        if (checkboxes.length > 0) {
            console.log('🔧 [SimpleSelectionTest] Testando seleção manual...');
            const firstCheckbox = checkboxes[0];
            const bookmarkItem = firstCheckbox.closest('.bookmark-item');
            
            if (bookmarkItem) {
                console.log(`   - Bookmark ID: ${bookmarkItem.dataset.id}`);
                
                // Marcar checkbox
                firstCheckbox.checked = true;
                
                // Chamar função de atualização diretamente
                if (window.UnifiedBookmarkSelection && window.UnifiedBookmarkSelection.updateBookmarkSelection) {
                    window.UnifiedBookmarkSelection.updateBookmarkSelection(bookmarkItem, true);
                    console.log(`   - Função chamada com sucesso`);
                    
                    // Verificar resultado
                    setTimeout(() => {
                        const setSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
                        console.log(`   - Set size após seleção: ${setSize}`);
                        
                        if (setSize > 0) {
                            console.log('✅ [SimpleSelectionTest] Seleção manual funcionou!');
                        } else {
                            console.log('❌ [SimpleSelectionTest] Seleção manual falhou!');
                        }
                    }, 100);
                } else {
                    console.log('❌ [SimpleSelectionTest] Função updateBookmarkSelection não encontrada');
                }
            }
        }
    }

    /**
     * Teste dos botões
     */
    function testButtons() {
        console.log('🧪 [SimpleSelectionTest] Testando botões...');
        
        const selectBtn = document.getElementById("selectAllBookmarksBtn");
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        
        if (!selectBtn) {
            console.log('❌ [SimpleSelectionTest] Botão não encontrado');
            return;
        }
        
        if (checkboxes.length === 0) {
            console.log('❌ [SimpleSelectionTest] Nenhum checkbox encontrado');
            return;
        }
        
        console.log('🔧 [SimpleSelectionTest] Simulando clique no botão...');
        selectBtn.click();
        
        // Verificar resultado após um tempo
        setTimeout(() => {
            const checkedCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
            const setSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
            
            console.log(`📊 [SimpleSelectionTest] Resultado do clique:`);
            console.log(`   - Checkboxes marcados: ${checkedCount}`);
            console.log(`   - Set size: ${setSize}`);
            
            if (checkedCount > 0 && setSize > 0) {
                console.log('✅ [SimpleSelectionTest] Botão funcionou!');
            } else {
                console.log('❌ [SimpleSelectionTest] Botão não funcionou!');
            }
        }, 500);
    }

    /**
     * Limpa todas as seleções para teste
     */
    function clearAll() {
        console.log('🧹 [SimpleSelectionTest] Limpando seleções...');
        
        // Desmarcar todos os checkboxes
        document.querySelectorAll('.bookmark-checkbox:checked').forEach(cb => {
            cb.checked = false;
        });
        
        // Limpar Set
        if (window.selectedBookmarkIds) {
            window.selectedBookmarkIds.clear();
        }
        
        // Remover classes visuais
        document.querySelectorAll('.bookmark-item.selected').forEach(item => {
            item.classList.remove('selected', 'sortable-selected');
        });
        
        console.log('✅ [SimpleSelectionTest] Limpeza concluída');
    }

    /**
     * Força seleção de todos sem usar botões
     */
    function forceSelectAll() {
        console.log('🔧 [SimpleSelectionTest] Forçando seleção de todos...');
        
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        let processed = 0;
        
        checkboxes.forEach((checkbox, index) => {
            const bookmarkItem = checkbox.closest('.bookmark-item');
            if (bookmarkItem && bookmarkItem.dataset.id) {
                // Marcar checkbox
                checkbox.checked = true;
                
                // Adicionar ao Set
                if (window.selectedBookmarkIds) {
                    window.selectedBookmarkIds.add(bookmarkItem.dataset.id);
                }
                
                // Adicionar classes visuais
                bookmarkItem.classList.add('selected', 'sortable-selected');
                
                processed++;
                console.log(`   - Processado ${index}: ${bookmarkItem.dataset.id}`);
            }
        });
        
        console.log(`📊 [SimpleSelectionTest] Processados ${processed} checkboxes`);
        
        // Atualizar contador se existir
        if (window.UnifiedBookmarkSelection && window.UnifiedBookmarkSelection.updateSelectedBookmarksCount) {
            window.UnifiedBookmarkSelection.updateSelectedBookmarksCount();
        }
        
        // Verificar resultado
        const setSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        const checkedCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
        
        console.log(`📊 [SimpleSelectionTest] Resultado final:`);
        console.log(`   - Set size: ${setSize}`);
        console.log(`   - Checkboxes marcados: ${checkedCount}`);
        
        if (setSize === checkedCount && setSize > 0) {
            console.log('✅ [SimpleSelectionTest] Seleção forçada funcionou!');
        } else {
            console.log('❌ [SimpleSelectionTest] Problema na seleção forçada!');
        }
    }

    /**
     * Teste completo
     */
    function runCompleteTest() {
        console.log('🚀 [SimpleSelectionTest] Executando teste completo...');
        
        // 1. Limpar tudo
        clearAll();
        
        // 2. Teste básico
        setTimeout(() => {
            basicTest();
            
            // 3. Teste de botões
            setTimeout(() => {
                testButtons();
            }, 1000);
        }, 500);
    }

    // API pública
    window.SimpleSelectionTest = {
        basicTest,
        testButtons,
        clearAll,
        forceSelectAll,
        runCompleteTest
    };

    console.log('[SimpleSelectionTest] Sistema carregado. Use SimpleSelectionTest.runCompleteTest() para testar.');

})();
