/**
 * Script de debug para testar a sincronização
 */

(function() {
    'use strict';

    // Função para testar se os sistemas estão funcionando
    function testSyncSystems() {
        console.log('=== TESTE DE SISTEMAS DE SINCRONIZAÇÃO ===');
        
        // Testar BookmarkSync
        if (window.BookmarkSync) {
            console.log('✓ BookmarkSync disponível');
            const stats = window.BookmarkSync.getStats();
            console.log('BookmarkSync stats:', stats);
        } else {
            console.log('✗ BookmarkSync não disponível');
        }
        
        // Testar FolderMonitor
        if (window.FolderMonitor) {
            console.log('✓ FolderMonitor disponível');
            const stats = window.FolderMonitor.getStats();
            console.log('FolderMonitor stats:', stats);
        } else {
            console.log('✗ FolderMonitor não disponível');
        }
        
        // Testar DynamicFolderRenderer
        if (window.DynamicFolderRenderer) {
            console.log('✓ DynamicFolderRenderer disponível');
            const stats = window.DynamicFolderRenderer.getStats();
            console.log('DynamicFolderRenderer stats:', stats);
        } else {
            console.log('✗ DynamicFolderRenderer não disponível');
        }
        
        // Testar DynamicBookmarkRenderer
        if (window.DynamicBookmarkRenderer) {
            console.log('✓ DynamicBookmarkRenderer disponível');
            const stats = window.DynamicBookmarkRenderer.getStats();
            console.log('DynamicBookmarkRenderer stats:', stats);
        } else {
            console.log('✗ DynamicBookmarkRenderer não disponível');
        }
        
        // Testar listeners de bookmarks
        console.log('Testando listeners de bookmarks...');
        
        // Criar uma pasta de teste
        chrome.bookmarks.create({
            title: 'Teste Sincronização - ' + Date.now(),
            parentId: '1' // Barra de favoritos
        }, (result) => {
            if (chrome.runtime.lastError) {
                console.error('Erro ao criar pasta de teste:', chrome.runtime.lastError);
                return;
            }
            
            console.log('Pasta de teste criada:', result);
            
            // Aguardar um pouco e depois remover
            setTimeout(() => {
                chrome.bookmarks.remove(result.id, () => {
                    if (chrome.runtime.lastError) {
                        console.error('Erro ao remover pasta de teste:', chrome.runtime.lastError);
                        return;
                    }
                    console.log('Pasta de teste removida');
                });
            }, 2000);
        });
    }
    
    // Função para forçar sincronização
    function forceSyncAll() {
        console.log('=== FORÇANDO SINCRONIZAÇÃO ===');
        
        if (window.BookmarkSync && typeof window.BookmarkSync.forceSync === 'function') {
            window.BookmarkSync.forceSync();
        }
        
        if (window.DynamicFolderRenderer && typeof window.DynamicFolderRenderer.forceReload === 'function') {
            window.DynamicFolderRenderer.forceReload();
        }
        
        if (window.FolderMonitor && typeof window.FolderMonitor.createSnapshot === 'function') {
            window.FolderMonitor.createSnapshot();
        }
    }
    
    // Função para testar sistema de seleção
    function testSelectionSystem() {
        console.log('=== TESTE DE SISTEMA DE SELEÇÃO ORIGINAL ===');

        // Testar variáveis globais
        console.log('selectedFolderIds:', typeof selectedFolderIds !== 'undefined' ? selectedFolderIds.size : 'não definido');
        console.log('selectedBookmarkIds:', typeof selectedBookmarkIds !== 'undefined' ? selectedBookmarkIds.size : 'não definido');

        // Testar checkboxes
        const folderCheckboxes = document.querySelectorAll('.folder-checkbox');
        const bookmarkCheckboxes = document.querySelectorAll('.bookmark-checkbox');

        console.log('Checkboxes encontrados:', {
            pastas: folderCheckboxes.length,
            favoritos: bookmarkCheckboxes.length
        });

        // Testar se as funções de seleção com Shift existem
        console.log('Funções de seleção com Shift:', {
            setupFolderShiftSelection: typeof setupFolderShiftSelection !== 'undefined',
            setupBookmarkShiftSelection: typeof setupBookmarkShiftSelection !== 'undefined'
        });

        // Testar se BookmarkSync está ativo
        console.log('Sistema de sincronização:', {
            BookmarkSync: !!window.BookmarkSync,
            BookmarkSyncInit: window.BookmarkSync && typeof window.BookmarkSync.init === 'function',
            updateFolderContents: typeof updateFolderContents !== 'undefined',
            reloadSelectedFolders: typeof reloadSelectedFolders !== 'undefined'
        });

        console.log('✅ Sistema original restaurado e funcionando!');
        console.log('💡 Teste: Selecione uma pasta, segure Shift e clique em outra para testar seleção múltipla');
        console.log('🔄 Teste sincronização: Crie/remova uma pasta no navegador e veja se atualiza na extensão');
    }

    // Comandos globais para debug
    window.testSyncSystems = testSyncSystems;
    window.forceSyncAll = forceSyncAll;
    window.testSelectionSystem = testSelectionSystem;
    
    // Auto-executar teste após carregamento
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testSyncSystems, 1000);
        });
    } else {
        setTimeout(testSyncSystems, 1000);
    }
    
    console.log('[DebugSync] Sistema de debug carregado');
    console.log('Use testSyncSystems() para testar os sistemas');
    console.log('Use forceSyncAll() para forçar sincronização');
    console.log('Use testSelectionSystem() para testar sistema de seleção');

})();
