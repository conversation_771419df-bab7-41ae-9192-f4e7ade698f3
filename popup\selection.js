/**
 * Sistema Unificado de Seleção de Favoritos
 * Gerencia toda a lógica de seleção para ambos os sistemas de renderização
 * (render.js e dynamic-bookmark-renderer.js)
 */

window.UnifiedBookmarkSelection = window.UnifiedBookmarkSelection || {};

(function() {
    'use strict';

    // Estado centralizado
    let lastClickedBookmarkCheckbox = null;
    const selectedBookmarkIds = new Set();
    let isInitialized = false;

    // Callbacks para sincronização com sistemas externos
    const syncCallbacks = new Set();

    // Tornar variáveis acessíveis globalmente para compatibilidade
    window.selectedBookmarkIds = selectedBookmarkIds;

    /**
     * Notifica sistemas externos sobre mudanças na seleção
     */
    function notifySelectionChange() {
        console.log(`[UnifiedBookmarkSelection] notifySelectionChange: ${selectedBookmarkIds.size} selecionados`);
        console.log(`[UnifiedBookmarkSelection] Callbacks registrados: ${syncCallbacks.size}`);

        syncCallbacks.forEach((callback, index) => {
            try {
                console.log(`[UnifiedBookmarkSelection] Chamando callback ${index}`);
                callback('selectionChanged', {
                    selectedIds: Array.from(selectedBookmarkIds),
                    count: selectedBookmarkIds.size
                });
            } catch (error) {
                console.error('[UnifiedBookmarkSelection] Erro em callback:', error);
            }
        });
    }

    /**
     * Função para atualizar lastClickedBookmarkCheckbox globalmente
     */
    function updateLastClickedBookmarkCheckbox(checkbox) {
        lastClickedBookmarkCheckbox = checkbox;
        window.lastClickedBookmarkCheckbox = checkbox;

        // Notificar sistemas externos
        syncCallbacks.forEach(callback => {
            try {
                callback('lastClickedChanged', checkbox);
            } catch (error) {
                console.error('[UnifiedBookmarkSelection] Erro em callback:', error);
            }
        });
    }

    /**
     * Inicializa o sistema unificado de seleção
     */
    function initBookmarkSelection() {
        if (isInitialized) return;

        console.log("[UnifiedBookmarkSelection] Inicializando sistema unificado...");

        // Aguardar DOM estar pronto se necessário
        if (document.readyState === 'loading') {
            console.log("[UnifiedBookmarkSelection] DOM ainda carregando, aguardando...");
            document.addEventListener('DOMContentLoaded', () => {
                console.log("[UnifiedBookmarkSelection] DOM carregado, inicializando...");
                performInitialization();
            });
        } else {
            performInitialization();
        }
    }

    /**
     * Executa a inicialização propriamente dita
     */
    function performInitialization() {
        console.log("[UnifiedBookmarkSelection] Executando inicialização...");

        // Configurar botões de seleção
        setupSelectionButtons();

        // Configurar seleção com Shift
        setupBookmarkShiftSelection();

        // Inicializar contador
        updateSelectedBookmarksCount();

        isInitialized = true;
        console.log("[UnifiedBookmarkSelection] Sistema inicializado com sucesso");
    }

    /**
     * Registra callback para sincronização com sistemas externos
     */
    function registerSyncCallback(callback) {
        syncCallbacks.add(callback);
    }

    /**
     * Remove callback de sincronização
     */
    function unregisterSyncCallback(callback) {
        syncCallbacks.delete(callback);
    }

    /**
     * Configura os botões de selecionar/desselecionar todos
     */
    function setupSelectionButtons() {
        console.log("[UnifiedBookmarkSelection] Configurando botões de seleção...");

        // Tentar encontrar botões com retry
        let attempts = 0;
        const maxAttempts = 5;

        function trySetupButtons() {
            attempts++;
            console.log(`[UnifiedBookmarkSelection] Tentativa ${attempts} de encontrar botões...`);

            // Botão "Selecionar todos"
            const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
            const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");

            if (selectAllBtn && deselectAllBtn) {
                console.log("[UnifiedBookmarkSelection] Ambos os botões encontrados!");

                // Remover listeners existentes se houver
                const newSelectAllBtn = selectAllBtn.cloneNode(true);
                const newDeselectAllBtn = deselectAllBtn.cloneNode(true);
                selectAllBtn.parentNode.replaceChild(newSelectAllBtn, selectAllBtn);
                deselectAllBtn.parentNode.replaceChild(newDeselectAllBtn, deselectAllBtn);

                // Adicionar novos listeners
                newSelectAllBtn.addEventListener("click", (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("[UnifiedBookmarkSelection] Clique em 'Selecionar todos'");
                    toggleAllBookmarks(true);
                });

                newDeselectAllBtn.addEventListener("click", (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("[UnifiedBookmarkSelection] Clique em 'Desselecionar todos'");
                    toggleAllBookmarks(false);
                });

                console.log("[UnifiedBookmarkSelection] Event listeners configurados com sucesso");
                return true;
            } else {
                console.warn(`[UnifiedBookmarkSelection] Botões não encontrados na tentativa ${attempts}`);
                console.warn(`[UnifiedBookmarkSelection] selectAllBtn: ${!!selectAllBtn}, deselectAllBtn: ${!!deselectAllBtn}`);

                if (attempts < maxAttempts) {
                    setTimeout(trySetupButtons, 100 * attempts); // Delay crescente
                    return false;
                } else {
                    console.error("[UnifiedBookmarkSelection] Falha ao encontrar botões após todas as tentativas");
                    return false;
                }
            }
        }

        trySetupButtons();
    }

    /**
     * Atualiza o contador de favoritos selecionados
     */
    function updateSelectedBookmarksCount() {
        const selectedCountEl = document.getElementById("selectedBookmarksCount");
        const checkedCount = document.querySelectorAll('.bookmark-checkbox:checked').length;

        console.log(`[UnifiedBookmarkSelection] Atualizando contador:`);
        console.log(`   - Set size: ${selectedBookmarkIds.size}`);
        console.log(`   - Checkboxes marcados: ${checkedCount}`);

        if (selectedCountEl) {
            selectedCountEl.textContent = `Selecionados: ${selectedBookmarkIds.size}`;

            // Adiciona classe visual para destacar quando há favoritos selecionados
            if (selectedBookmarkIds.size > 0) {
                selectedCountEl.classList.add("has-selected");
            } else {
                selectedCountEl.classList.remove("has-selected");
            }

            console.log(`   - Contador atualizado: ${selectedCountEl.textContent}`);
        } else {
            console.warn(`[UnifiedBookmarkSelection] Elemento selectedBookmarksCount não encontrado`);
        }
    }

    /**
     * Seleciona ou desseleciona todos os favoritos visíveis
     * @param {boolean} select - true para selecionar, false para desselecionar
     */
    function toggleAllBookmarks(select) {
        console.log(`[UnifiedBookmarkSelection] toggleAllBookmarks(${select})`);

        const visibleCheckboxes = Array.from(document.querySelectorAll('.bookmark-checkbox'))
            .filter(checkbox => {
                const bookmarkItem = checkbox.closest('.bookmark-item');
                return bookmarkItem && bookmarkItem.style.display !== 'none';
            });

        console.log(`[UnifiedBookmarkSelection] Encontrados ${visibleCheckboxes.length} checkboxes visíveis`);

        if (visibleCheckboxes.length === 0) {
            console.warn("[UnifiedBookmarkSelection] Nenhum checkbox visível encontrado");
            if (typeof showActionFeedback === 'function') {
                showActionFeedback("Nenhum favorito visível para selecionar", "info");
            }
            return;
        }

        // Captura quantos estavam selecionados antes de desmarcar
        let prevSelected = 0;
        if (!select) {
            prevSelected = visibleCheckboxes.filter(checkbox => checkbox.checked).length;
        }

        let processedCount = 0;

        // Primeiro, atualizar todos os checkboxes
        visibleCheckboxes.forEach((checkbox, index) => {
            if (checkbox.checked !== select) {
                checkbox.checked = select;
                processedCount++;
                console.log(`[UnifiedBookmarkSelection] Checkbox ${index} marcado como: ${select}`);
            }
        });

        // Depois, atualizar o Set de seleção
        visibleCheckboxes.forEach((checkbox, index) => {
            const bookmarkItem = checkbox.closest('.bookmark-item');
            if (bookmarkItem && bookmarkItem.dataset.id) {
                const bookmarkId = bookmarkItem.dataset.id;

                if (select) {
                    selectedBookmarkIds.add(bookmarkId);
                    bookmarkItem.classList.add("selected", "sortable-selected");
                    console.log(`[UnifiedBookmarkSelection] Adicionado ${bookmarkId} ao Set`);
                } else {
                    selectedBookmarkIds.delete(bookmarkId);
                    bookmarkItem.classList.remove("selected", "sortable-selected");
                    console.log(`[UnifiedBookmarkSelection] Removido ${bookmarkId} do Set`);
                }
            }
        });

        console.log(`[UnifiedBookmarkSelection] Processados ${processedCount} checkboxes`);
        console.log(`[UnifiedBookmarkSelection] selectedBookmarkIds.size: ${selectedBookmarkIds.size}`);

        // Se alguma checkbox foi selecionada, atualizar o último clicado
        if (visibleCheckboxes.length > 0) {
            updateLastClickedBookmarkCheckbox(visibleCheckboxes[0]);
        }

        // Mostrar feedback visual
        if (typeof showActionFeedback === 'function') {
            if (select) {
                showActionFeedback(`${processedCount} favoritos selecionados`, "success");
            } else {
                showActionFeedback(`${prevSelected} favoritos desmarcados`, "info");
            }
        } else {
            console.warn("[UnifiedBookmarkSelection] showActionFeedback não disponível");
        }

        // Notificar mudança
        notifySelectionChange();
    }

    /**
     * Configura a seleção de favoritos usando a tecla Shift
     */
    function setupBookmarkShiftSelection() {
        const bookmarksContainer = document.getElementById("bookmarksContainer");
        if (!bookmarksContainer) return;

        bookmarksContainer.addEventListener("click", (e) => {
            // Verificar se o clique foi em um item de favorito e se Shift está pressionado
            const bookmarkItem = e.target.closest(".bookmark-item");
            if (!bookmarkItem || !e.shiftKey) return;

            const currentCheckbox = bookmarkItem.querySelector(".bookmark-checkbox");
            if (!currentCheckbox) return;

            // Se há um checkbox anterior clicado, fazer seleção em range
            if (lastClickedBookmarkCheckbox) {
                // Obter todos os checkboxes visíveis
                const checkboxes = Array.from(document.querySelectorAll(".bookmark-checkbox"))
                    .filter(cb => {
                        // Verificar se o checkbox está visível
                        const container = cb.closest(".bookmark-item");
                        return container && window.getComputedStyle(container).display !== "none";
                    });

                // Encontrar índices do checkbox atual e do último clicado
                const currentIndex = checkboxes.indexOf(currentCheckbox);
                const lastIndex = checkboxes.indexOf(lastClickedBookmarkCheckbox);

                if (currentIndex !== -1 && lastIndex !== -1) {
                    console.log(`[UnifiedBookmarkSelection] Shift+Click: range ${lastIndex} → ${currentIndex}`);

                    // Determinar o range de seleção
                    const startIndex = Math.min(currentIndex, lastIndex);
                    const endIndex = Math.max(currentIndex, lastIndex);

                    // Determinar o estado baseado no último checkbox clicado (não no atual)
                    const selectState = lastClickedBookmarkCheckbox.checked;
                    console.log(`[UnifiedBookmarkSelection] Estado base: ${selectState}`);

                    // Aplicar o estado a todos os checkboxes no range
                    for (let i = startIndex; i <= endIndex; i++) {
                        const checkbox = checkboxes[i];
                        const item = checkbox.closest('.bookmark-item');

                        if (item) {
                            checkbox.checked = selectState;
                            updateBookmarkSelection(item, selectState);
                            console.log(`[UnifiedBookmarkSelection] Range ${i}: ${item.dataset.id} → ${selectState}`);
                        }
                    }

                    updateSelectedBookmarksCount();
                    notifySelectionChange();
                }
            }

            // Atualizar o último checkbox clicado
            setTimeout(() => {
                updateLastClickedBookmarkCheckbox(currentCheckbox);
            }, 0);
        });
    }

// FUNÇÕES GLOBAIS REMOVIDAS - DUPLICADAS COM AS INTERNAS
// Todas as funções agora estão dentro do sistema unificado

// FUNÇÃO DUPLICADA REMOVIDA - Versão correta está dentro do escopo do sistema unificado

    /**
     * Atualiza o estado de seleção de um favorito
     * @param {HTMLElement} bookmarkItem - Elemento do favorito
     * @param {boolean} selected - Estado de seleção
     */
    function updateBookmarkSelection(bookmarkItem, selected) {
        if (!bookmarkItem) {
            console.error("[UnifiedBookmarkSelection] updateBookmarkSelection: bookmarkItem é null");
            return;
        }

        const bookmarkId = bookmarkItem.dataset.id;
        if (!bookmarkId) {
            console.error("[UnifiedBookmarkSelection] updateBookmarkSelection: bookmarkId não encontrado", bookmarkItem);
            return;
        }

        console.log(`[UnifiedBookmarkSelection] updateBookmarkSelection: ${bookmarkId} -> ${selected}`);

        if (selected) {
            selectedBookmarkIds.add(bookmarkId);
            bookmarkItem.classList.add("selected", "sortable-selected");
            console.log(`[UnifiedBookmarkSelection] Adicionado ${bookmarkId} à seleção`);
        } else {
            selectedBookmarkIds.delete(bookmarkId);
            bookmarkItem.classList.remove("selected", "sortable-selected");
            console.log(`[UnifiedBookmarkSelection] Removido ${bookmarkId} da seleção`);
        }

        console.log(`[UnifiedBookmarkSelection] selectedBookmarkIds.size agora: ${selectedBookmarkIds.size}`);

        // Notificar mudança
        notifySelectionChange();
    }

    /**
     * Limpa todas as seleções de favoritos
     */
    function clearBookmarkSelection() {
        selectedBookmarkIds.clear();

        // Remover classes visuais de seleção
        document.querySelectorAll('.bookmark-item.selected').forEach(item => {
            item.classList.remove('selected', 'sortable-selected');
        });

        // Desmarcar todos os checkboxes
        document.querySelectorAll('.bookmark-checkbox:checked').forEach(checkbox => {
            checkbox.checked = false;
        });

        updateSelectedBookmarksCount();
        notifySelectionChange();
    }

    /**
     * Obtém os IDs dos favoritos selecionados
     * @returns {Array} Array com os IDs dos favoritos selecionados
     */
    function getSelectedBookmarkIds() {
        return Array.from(selectedBookmarkIds);
    }

    /**
     * Obtém os elementos DOM dos favoritos selecionados
     * @returns {Array} Array com os elementos DOM dos favoritos selecionados
     */
    function getSelectedBookmarkElements() {
        return getSelectedBookmarkIds().map(id =>
            document.querySelector(`.bookmark-item[data-id="${id}"]`)
        ).filter(element => element !== null);
    }

    /**
     * Verifica se há favoritos selecionados
     * @returns {boolean} true se há favoritos selecionados
     */
    function hasSelectedBookmarks() {
        return selectedBookmarkIds.size > 0;
    }

    /**
     * Seleciona favoritos por IDs
     * @param {Array} bookmarkIds - Array de IDs de favoritos para selecionar
     */
    function selectBookmarksByIds(bookmarkIds) {
        bookmarkIds.forEach(id => {
            const bookmarkItem = document.querySelector(`.bookmark-item[data-id="${id}"]`);
            if (bookmarkItem) {
                const checkbox = bookmarkItem.querySelector('.bookmark-checkbox');
                if (checkbox) {
                    checkbox.checked = true;
                    updateBookmarkSelection(bookmarkItem, true);
                }
            }
        });

        updateSelectedBookmarksCount();
    }

    // API pública
    window.UnifiedBookmarkSelection = {
        init: initBookmarkSelection,
        registerSyncCallback,
        unregisterSyncCallback,
        updateBookmarkSelection,
        clearBookmarkSelection,
        getSelectedBookmarkIds,
        getSelectedBookmarkElements,
        hasSelectedBookmarks,
        selectBookmarksByIds,
        updateLastClickedBookmarkCheckbox,
        updateSelectedBookmarksCount: () => updateSelectedBookmarksCount()
    };

    // Funções globais para compatibilidade com código existente
    window.initBookmarkSelection = initBookmarkSelection;
    window.updateBookmarkSelection = updateBookmarkSelection;
    window.clearBookmarkSelection = clearBookmarkSelection;
    window.getSelectedBookmarkIds = getSelectedBookmarkIds;
    window.getSelectedBookmarkElements = getSelectedBookmarkElements;
    window.hasSelectedBookmarks = hasSelectedBookmarks;
    window.selectBookmarksByIds = selectBookmarksByIds;
    window.updateLastClickedBookmarkCheckbox = updateLastClickedBookmarkCheckbox;
    window.updateSelectedBookmarksCount = () => updateSelectedBookmarksCount();

})();
