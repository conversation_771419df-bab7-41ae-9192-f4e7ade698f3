/**
 * Camada de Compatibilidade para Sistema de Bookmarks
 * 
 * Fornece funções de compatibilidade para que o código existente
 * continue funcionando com o novo sistema de renderização dinâmica.
 */

(function() {
    'use strict';

    /**
     * Função de compatibilidade para renderBookmarks
     * Redireciona para o novo sistema de renderização dinâmica
     * @param {Array} bookmarks - Array de bookmarks (ignorado no novo sistema)
     * @param {HTMLElement} container - Container dos bookmarks
     * @param {boolean} append - Se deve adicionar ao final (ignorado)
     * @param {string} folderId - ID da pasta
     * @param {boolean} preserveCache - Se deve preservar cache (ignorado)
     */
    window.renderBookmarks = function(bookmarks, container, append = false, folderId, preserveCache = false) {
        console.log('[BookmarkCompatibility] Redirecionando renderBookmarks para DynamicBookmarkRenderer');
        
        if (window.DynamicBookmarkRenderer) {
            // Se o sistema dinâmico não foi inicializado, inicializar agora
            if (!window.DynamicBookmarkRenderer.getStats().initialized) {
                window.DynamicBookmarkRenderer.init(container);
            }
            
            // Se folderId foi fornecido, carregar apenas essa pasta
            if (folderId) {
                const currentFolders = window.DynamicFolderRenderer ? 
                    window.DynamicFolderRenderer.getSelectedFolders() : 
                    (typeof selectedFolderIds !== 'undefined' ? selectedFolderIds : new Set());
                
                // Garantir que a pasta está na seleção
                const foldersToLoad = new Set(currentFolders);
                foldersToLoad.add(folderId);
                
                window.DynamicBookmarkRenderer.loadBookmarksFromFolders(foldersToLoad);
            } else {
                // Carregar de todas as pastas selecionadas
                const selectedFolders = window.DynamicFolderRenderer ? 
                    window.DynamicFolderRenderer.getSelectedFolders() : 
                    (typeof selectedFolderIds !== 'undefined' ? selectedFolderIds : new Set());
                
                if (selectedFolders.size > 0) {
                    window.DynamicBookmarkRenderer.loadBookmarksFromFolders(selectedFolders);
                } else {
                    window.DynamicBookmarkRenderer.clearAllBookmarks();
                }
            }
        } else {
            console.warn('[BookmarkCompatibility] DynamicBookmarkRenderer não disponível, usando fallback');
            renderBookmarksFallback(bookmarks, container, append, folderId, preserveCache);
        }
    };

    /**
     * Função de compatibilidade para updateFolderContents
     * @param {string} folderId - ID da pasta
     * @param {boolean} reuseCache - Se deve reutilizar cache (ignorado)
     */
    window.updateFolderContents = function(folderId, reuseCache = true) {
        console.log('[BookmarkCompatibility] Redirecionando updateFolderContents para DynamicBookmarkRenderer');
        
        if (window.DynamicBookmarkRenderer) {
            window.DynamicBookmarkRenderer.loadBookmarksFromFolders([folderId]);
        } else {
            console.warn('[BookmarkCompatibility] DynamicBookmarkRenderer não disponível');
            updateFolderContentsFallback(folderId, reuseCache);
        }
    };

    /**
     * Função de compatibilidade para reloadSelectedFolders
     * @param {boolean} preserveScroll - Se deve preservar scroll (ignorado no novo sistema)
     */
    window.reloadSelectedFolders = function(preserveScroll = false) {
        console.log('[BookmarkCompatibility] Redirecionando reloadSelectedFolders para DynamicBookmarkRenderer');
        
        if (window.DynamicBookmarkRenderer) {
            const selectedFolders = window.DynamicFolderRenderer ? 
                window.DynamicFolderRenderer.getSelectedFolders() : 
                (typeof selectedFolderIds !== 'undefined' ? selectedFolderIds : new Set());
            
            window.DynamicBookmarkRenderer.loadBookmarksFromFolders(selectedFolders);
        } else {
            console.warn('[BookmarkCompatibility] DynamicBookmarkRenderer não disponível');
            reloadSelectedFoldersFallback(preserveScroll);
        }
    };

    /**
     * Função de compatibilidade para updateBookmarkCount
     */
    window.updateBookmarkCount = function() {
        if (window.DynamicBookmarkRenderer) {
            // O sistema dinâmico já atualiza automaticamente
            return;
        }
        
        // Fallback: contar elementos na interface
        const container = document.getElementById("bookmarksContainer");
        const countElement = document.getElementById('bookmarksDisplayCount');
        
        if (container && countElement) {
            const bookmarkCount = container.querySelectorAll('.bookmark-item').length;
            countElement.textContent = `Favoritos exibidos: ${bookmarkCount}`;
            
            if (bookmarkCount > 0) {
                countElement.classList.add('has-bookmarks');
            } else {
                countElement.classList.remove('has-bookmarks');
            }
        }
    };

    /**
     * Função de compatibilidade para clearAllBookmarks
     */
    window.clearAllBookmarks = function() {
        console.log('[BookmarkCompatibility] Redirecionando clearAllBookmarks para DynamicBookmarkRenderer');
        
        if (window.DynamicBookmarkRenderer) {
            window.DynamicBookmarkRenderer.clearAllBookmarks();
        } else {
            // Fallback
            const container = document.getElementById("bookmarksContainer");
            if (container) {
                container.innerHTML = '';
            }
            
            if (typeof bookmarkElementCache !== 'undefined') {
                bookmarkElementCache.clear();
            }
            
            updateBookmarkCount();
        }
    };

    /**
     * REMOVIDO - getSelectedBookmarkIds já definido em selection.js
     * Evitando conflito de redefinição
     */

    /**
     * Função de compatibilidade para setSelectedBookmarks
     * @param {Array|Set} bookmarks - Bookmarks para selecionar
     */
    window.setSelectedBookmarks = function(bookmarks) {
        if (window.DynamicBookmarkRenderer) {
            window.DynamicBookmarkRenderer.setSelectedBookmarks(bookmarks);
        } else {
            // Fallback: atualizar interface diretamente
            const bookmarkArray = Array.isArray(bookmarks) ? bookmarks : Array.from(bookmarks);
            
            // Limpar seleções atuais
            document.querySelectorAll('.bookmark-item.selected').forEach(el => {
                el.classList.remove('selected');
                const checkbox = el.querySelector('.bookmark-checkbox');
                if (checkbox) checkbox.checked = false;
            });
            
            // Aplicar novas seleções
            bookmarkArray.forEach(bookmarkId => {
                const element = document.querySelector(`[data-id="${bookmarkId}"]`);
                if (element) {
                    element.classList.add('selected');
                    const checkbox = element.querySelector('.bookmark-checkbox');
                    if (checkbox) checkbox.checked = true;
                }
            });
        }
    };

    /**
     * Função fallback para renderização de bookmarks (sistema antigo)
     */
    function renderBookmarksFallback(bookmarks, container, append, folderId, preserveCache) {
        // Implementação simplificada do sistema antigo
        if (!append) {
            if (folderId) {
                const elementsToRemove = container.querySelectorAll(`[data-folder="${folderId}"]`);
                elementsToRemove.forEach(el => el.remove());
            } else {
                container.innerHTML = '';
            }
        }

        if (!bookmarks || bookmarks.length === 0) {
            updateBookmarkCount();
            return;
        }

        const fragment = document.createDocumentFragment();
        
        bookmarks.forEach(bookmark => {
            if (!bookmark.url) return;
            
            const element = createBookmarkElementFallback(bookmark, folderId);
            if (element) {
                fragment.appendChild(element);
            }
        });

        container.appendChild(fragment);
        updateBookmarkCount();
    }

    /**
     * Cria elemento de bookmark (fallback)
     */
    function createBookmarkElementFallback(bookmark, folderId) {
        const item = document.createElement("div");
        item.className = "bookmark-item";
        item.dataset.id = bookmark.id;
        item.dataset.folder = folderId;

        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.className = "bookmark-checkbox";

        const textContainer = document.createElement("div");
        textContainer.className = "text-container";

        const titleContainer = document.createElement("div");
        titleContainer.className = "title-container";

        const favicon = document.createElement("img");
        favicon.className = "favicon";
        favicon.src = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23999' d='M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8z'/%3E%3C/svg%3E";

        const titleLink = document.createElement("a");
        titleLink.className = "bookmark-link";
        titleLink.href = bookmark.url;
        titleLink.textContent = bookmark.title || bookmark.url;
        titleLink.target = "_blank";

        const urlContainer = document.createElement("div");
        urlContainer.className = "url-container";
        urlContainer.textContent = bookmark.url;

        titleContainer.appendChild(favicon);
        titleContainer.appendChild(titleLink);
        textContainer.appendChild(titleContainer);
        textContainer.appendChild(urlContainer);

        item.appendChild(checkbox);
        item.appendChild(textContainer);

        return item;
    }

    /**
     * Fallback para updateFolderContents
     */
    function updateFolderContentsFallback(folderId, reuseCache) {
        chrome.bookmarks.getChildren(folderId, (children) => {
            if (chrome.runtime.lastError) {
                console.error("Erro ao obter favoritos:", chrome.runtime.lastError);
                return;
            }
            
            const bookmarks = children
                .filter(bookmark => bookmark.url)
                .sort((a, b) => (a.index || 0) - (b.index || 0));

            const container = document.getElementById('bookmarksContainer');
            if (container) {
                renderBookmarksFallback(bookmarks, container, false, folderId, reuseCache);
            }
        });
    }

    /**
     * Fallback para reloadSelectedFolders
     */
    function reloadSelectedFoldersFallback(preserveScroll) {
        const container = document.getElementById('bookmarksContainer');
        if (!container) return;

        container.innerHTML = '';

        if (typeof selectedFolderIds === 'undefined' || selectedFolderIds.size === 0) {
            updateBookmarkCount();
            return;
        }

        Array.from(selectedFolderIds).forEach(folderId => {
            chrome.bookmarks.getChildren(folderId, (children) => {
                if (!chrome.runtime.lastError) {
                    const items = children
                        .filter(c => c.url)
                        .sort((a, b) => (a.index || 0) - (b.index || 0));
                    renderBookmarksFallback(items, container, true, folderId, false);
                }
            });
        });
    }

    console.log('[BookmarkCompatibility] Camada de compatibilidade carregada');

})();
