/**
 * FolderMonitor - Versão Simplificada
 * Monitora mudanças nas pastas de bookmarks de forma simples e direta
 */

(function() {
    'use strict';

    // Configurações simples
    const CONFIG = {
        CHECK_INTERVAL: 5000, // 5 segundos
        ENABLE_LOGS: true,
        MAX_PROCESSING_TIME: 10000, // 10 segundos máximo
        MONITOR_ENABLED: true
    };

    // Estado simples
    let isMonitoring = false;
    let monitorInterval = null;
    let lastFolderSnapshot = new Map();
    let lastBookmarkSnapshot = new Map();
    let isProcessing = false;
    let isUserSelecting = false;

    /**
     * Inicia o monitoramento
     */
    function startMonitoring() {
        if (isMonitoring || !CONFIG.MONITOR_ENABLED) return;

        console.log('[FolderMonitor] Iniciando monitoramento simples...');

        // Criar snapshot inicial
        createInitialSnapshot();

        // Iniciar verificação periódica
        monitorInterval = setInterval(checkForChanges, CONFIG.CHECK_INTERVAL);
        isMonitoring = true;

        console.log('[FolderMonitor] Monitoramento ativo');
    }

    /**
     * Para o monitoramento
     */
    function stopMonitoring() {
        if (!isMonitoring) return;

        if (monitorInterval) {
            clearInterval(monitorInterval);
            monitorInterval = null;
        }

        isMonitoring = false;
        isProcessing = false;

        console.log('[FolderMonitor] Monitoramento parado');
    }

    /**
     * Verificação simples de mudanças
     */
    async function checkForChanges() {
        if (isProcessing || isUserSelecting) return;

        isProcessing = true;
        const startTime = Date.now();

        try {
            const tree = await getBookmarkTree();
            const currentFolders = new Map();
            const currentBookmarks = new Map();

            // Processar árvore de forma simples
            await processTreeSimple(tree[0].children, currentFolders, currentBookmarks);

            // Detectar mudanças
            detectSimpleChanges(currentFolders, currentBookmarks);

            // Atualizar snapshots
            lastFolderSnapshot = currentFolders;
            lastBookmarkSnapshot = currentBookmarks;

        } catch (error) {
            console.error('[FolderMonitor] Erro:', error);
        } finally {
            isProcessing = false;
            const processingTime = Date.now() - startTime;
            if (CONFIG.ENABLE_LOGS && processingTime > 1000) {
                console.log(`[FolderMonitor] Processamento: ${processingTime}ms`);
            }
        }
    }

    /**
     * Processa árvore de forma simples
     */
    async function processTreeSimple(nodes, folders, bookmarks, depth = 0) {
        if (!nodes || depth > 10) return; // Limite simples de profundidade

        for (const node of nodes) {
            if (node.children) {
                // É uma pasta
                folders.set(node.id, {
                    id: node.id,
                    title: node.title,
                    parentId: node.parentId
                });
                
                // Processar filhos
                await processTreeSimple(node.children, folders, bookmarks, depth + 1);
            } else if (node.url) {
                // É um bookmark
                bookmarks.set(node.id, {
                    id: node.id,
                    title: node.title,
                    url: node.url,
                    parentId: node.parentId
                });
            }

            // Yield ocasional para não travar
            if (depth === 0) {
                await new Promise(resolve => setTimeout(resolve, 0));
            }
        }
    }

    /**
     * Detecta mudanças de forma simples
     */
    function detectSimpleChanges(currentFolders, currentBookmarks) {
        // Detectar pastas renomeadas
        for (const [id, folder] of currentFolders) {
            const oldFolder = lastFolderSnapshot.get(id);
            if (oldFolder && oldFolder.title !== folder.title) {
                handleFolderRenamed(id, folder, oldFolder);
            }
        }

        // Detectar bookmarks renomeados
        for (const [id, bookmark] of currentBookmarks) {
            const oldBookmark = lastBookmarkSnapshot.get(id);
            if (oldBookmark && oldBookmark.title !== bookmark.title) {
                handleBookmarkRenamed(id, bookmark, oldBookmark);
            }
        }
    }

    /**
     * Cria snapshot inicial
     */
    async function createInitialSnapshot() {
        try {
            const tree = await getBookmarkTree();
            lastFolderSnapshot.clear();
            lastBookmarkSnapshot.clear();
            
            await processTreeSimple(tree[0].children, lastFolderSnapshot, lastBookmarkSnapshot);
            
            if (CONFIG.ENABLE_LOGS) {
                console.log(`[FolderMonitor] Snapshot: ${lastFolderSnapshot.size} pastas, ${lastBookmarkSnapshot.size} bookmarks`);
            }
        } catch (error) {
            console.error('[FolderMonitor] Erro no snapshot:', error);
        }
    }

    /**
     * Trata renomeação de pasta
     */
    function handleFolderRenamed(folderId, newData, oldData) {
        if (CONFIG.ENABLE_LOGS) {
            console.log(`[FolderMonitor] Pasta renomeada: "${oldData.title}" → "${newData.title}"`);
        }
        
        // Mostrar feedback simples
        if (typeof showActionFeedback === 'function') {
            showActionFeedback(`Pasta renomeada: "${oldData.title}" → "${newData.title}"`, "info", 2000, false, 'rename');
        }
    }

    /**
     * Trata renomeação de bookmark
     */
    function handleBookmarkRenamed(bookmarkId, newData, oldData) {
        if (CONFIG.ENABLE_LOGS) {
            console.log(`[FolderMonitor] Bookmark renomeado: "${oldData.title}" → "${newData.title}"`);
        }
    }

    /**
     * Função auxiliar para obter árvore de bookmarks
     */
    function getBookmarkTree() {
        return new Promise((resolve, reject) => {
            chrome.bookmarks.getTree((tree) => {
                if (chrome.runtime.lastError) {
                    reject(new Error(chrome.runtime.lastError.message));
                } else {
                    resolve(tree);
                }
            });
        });
    }

    /**
     * Controle de seleção do usuário
     */
    function setUserSelecting(selecting) {
        isUserSelecting = selecting;
        if (CONFIG.ENABLE_LOGS) {
            console.log(`[FolderMonitor] Modo seleção: ${selecting ? 'ATIVADO' : 'DESATIVADO'}`);
        }
    }

    /**
     * Estatísticas simples
     */
    function getStats() {
        return {
            monitoring: isMonitoring,
            processing: isProcessing,
            folders: lastFolderSnapshot.size,
            bookmarks: lastBookmarkSnapshot.size,
            userSelecting: isUserSelecting
        };
    }

    // API pública simplificada
    window.FolderMonitor = {
        start: startMonitoring,
        stop: stopMonitoring,
        getStats: getStats,
        createSnapshot: createInitialSnapshot,
        setUserSelecting: setUserSelecting
    };

    // Comandos globais simples
    window.startFolderMonitor = () => startMonitoring();
    window.stopFolderMonitor = () => stopMonitoring();
    window.folderMonitorStats = () => {
        const stats = getStats();
        console.table(stats);
        return stats;
    };

    console.log('[FolderMonitor] Sistema simplificado carregado');

})();
