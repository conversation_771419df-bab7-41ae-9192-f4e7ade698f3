/**
 * Teste e Correção Direta da Seleção Múltipla
 * Implementação simplificada para identificar e corrigir o problema
 */

window.SelectionFixTest = window.SelectionFixTest || {};

(function() {
    'use strict';

    /**
     * Implementação simplificada de seleção de todos
     */
    function simpleSelectAll() {
        console.log('🔧 [SelectionFixTest] Executando seleção simples de todos...');
        
        // Encontrar todos os checkboxes
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        console.log(`📊 [SelectionFixTest] Encontrados ${checkboxes.length} checkboxes`);
        
        if (checkboxes.length === 0) {
            console.warn('⚠️ [SelectionFixTest] Nenhum checkbox encontrado');
            return;
        }
        
        let processed = 0;
        
        // Marcar todos os checkboxes
        checkboxes.forEach((checkbox, index) => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                processed++;
                console.log(`✅ [SelectionFixTest] Marcado checkbox ${index}`);
                
                // Adicionar ao Set global se existir
                if (window.selectedBookmarkIds) {
                    const bookmarkItem = checkbox.closest('.bookmark-item');
                    if (bookmarkItem && bookmarkItem.dataset.id) {
                        window.selectedBookmarkIds.add(bookmarkItem.dataset.id);
                        bookmarkItem.classList.add('selected', 'sortable-selected');
                        console.log(`✅ [SelectionFixTest] Adicionado ${bookmarkItem.dataset.id} ao Set`);
                    }
                }
            }
        });
        
        console.log(`📊 [SelectionFixTest] Processados ${processed} checkboxes`);
        
        // Atualizar contador se a função existir
        if (typeof window.updateSelectedBookmarksCount === 'function') {
            window.updateSelectedBookmarksCount();
            console.log('✅ [SelectionFixTest] Contador atualizado');
        }
        
        // Verificar resultado final
        const finalSetSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        const checkedCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
        
        console.log(`📊 [SelectionFixTest] Resultado final:`);
        console.log(`   - Set size: ${finalSetSize}`);
        console.log(`   - Checkboxes marcados: ${checkedCount}`);
        
        if (finalSetSize === checkedCount && finalSetSize > 0) {
            console.log('✅ [SelectionFixTest] Seleção funcionou corretamente!');
        } else {
            console.error('❌ [SelectionFixTest] Problema na seleção detectado');
        }
    }

    /**
     * Implementação simplificada de desseleção de todos
     */
    function simpleDeselectAll() {
        console.log('🔧 [SelectionFixTest] Executando desseleção simples de todos...');
        
        // Encontrar todos os checkboxes marcados
        const checkedCheckboxes = document.querySelectorAll('.bookmark-checkbox:checked');
        console.log(`📊 [SelectionFixTest] Encontrados ${checkedCheckboxes.length} checkboxes marcados`);
        
        let processed = 0;
        
        // Desmarcar todos os checkboxes
        checkedCheckboxes.forEach((checkbox, index) => {
            checkbox.checked = false;
            processed++;
            console.log(`✅ [SelectionFixTest] Desmarcado checkbox ${index}`);
            
            // Remover do Set global se existir
            if (window.selectedBookmarkIds) {
                const bookmarkItem = checkbox.closest('.bookmark-item');
                if (bookmarkItem && bookmarkItem.dataset.id) {
                    window.selectedBookmarkIds.delete(bookmarkItem.dataset.id);
                    bookmarkItem.classList.remove('selected', 'sortable-selected');
                    console.log(`✅ [SelectionFixTest] Removido ${bookmarkItem.dataset.id} do Set`);
                }
            }
        });
        
        console.log(`📊 [SelectionFixTest] Processados ${processed} checkboxes`);
        
        // Atualizar contador se a função existir
        if (typeof window.updateSelectedBookmarksCount === 'function') {
            window.updateSelectedBookmarksCount();
            console.log('✅ [SelectionFixTest] Contador atualizado');
        }
        
        // Verificar resultado final
        const finalSetSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        const checkedCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
        
        console.log(`📊 [SelectionFixTest] Resultado final:`);
        console.log(`   - Set size: ${finalSetSize}`);
        console.log(`   - Checkboxes marcados: ${checkedCount}`);
        
        if (finalSetSize === 0 && checkedCount === 0) {
            console.log('✅ [SelectionFixTest] Desseleção funcionou corretamente!');
        } else {
            console.error('❌ [SelectionFixTest] Problema na desseleção detectado');
        }
    }

    /**
     * Substitui os event listeners dos botões por implementações simples
     */
    function replaceButtonListeners() {
        console.log('🔧 [SelectionFixTest] Substituindo event listeners dos botões...');
        
        const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
        const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");
        
        if (selectAllBtn) {
            // Remover listeners existentes clonando o elemento
            const newSelectAllBtn = selectAllBtn.cloneNode(true);
            selectAllBtn.parentNode.replaceChild(newSelectAllBtn, selectAllBtn);
            
            // Adicionar novo listener simples
            newSelectAllBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔧 [SelectionFixTest] Clique no botão Selecionar Todos');
                simpleSelectAll();
            });
            
            console.log('✅ [SelectionFixTest] Listener do botão "Selecionar" substituído');
        } else {
            console.error('❌ [SelectionFixTest] Botão "Selecionar" não encontrado');
        }
        
        if (deselectAllBtn) {
            // Remover listeners existentes clonando o elemento
            const newDeselectAllBtn = deselectAllBtn.cloneNode(true);
            deselectAllBtn.parentNode.replaceChild(newDeselectAllBtn, deselectAllBtn);
            
            // Adicionar novo listener simples
            newDeselectAllBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔧 [SelectionFixTest] Clique no botão Desselecionar Todos');
                simpleDeselectAll();
            });
            
            console.log('✅ [SelectionFixTest] Listener do botão "Desselecionar" substituído');
        } else {
            console.error('❌ [SelectionFixTest] Botão "Desselecionar" não encontrado');
        }
    }

    /**
     * Executa um teste completo da seleção múltipla
     */
    function runCompleteTest() {
        console.log('🧪 [SelectionFixTest] Executando teste completo...');
        
        // 1. Verificar estado inicial
        console.log('📊 [SelectionFixTest] Estado inicial:');
        const initialChecked = document.querySelectorAll('.bookmark-checkbox:checked').length;
        const initialSetSize = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        console.log(`   - Checkboxes marcados: ${initialChecked}`);
        console.log(`   - Set size: ${initialSetSize}`);
        
        // 2. Testar seleção de todos
        console.log('🔧 [SelectionFixTest] Testando seleção de todos...');
        simpleSelectAll();
        
        // 3. Aguardar um pouco e testar desseleção
        setTimeout(() => {
            console.log('🔧 [SelectionFixTest] Testando desseleção de todos...');
            simpleDeselectAll();
            
            // 4. Resultado final
            setTimeout(() => {
                console.log('📊 [SelectionFixTest] Teste completo finalizado');
            }, 100);
        }, 500);
    }

    /**
     * Verifica se os elementos necessários existem
     */
    function checkElements() {
        console.log('🔍 [SelectionFixTest] Verificando elementos...');
        
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        const selectBtn = document.getElementById("selectAllBookmarksBtn");
        const deselectBtn = document.getElementById("deselectAllBookmarksBtn");
        const selectedBookmarkIds = window.selectedBookmarkIds;
        
        console.log(`📊 [SelectionFixTest] Elementos encontrados:`);
        console.log(`   - Checkboxes: ${checkboxes.length}`);
        console.log(`   - Botão Selecionar: ${!!selectBtn}`);
        console.log(`   - Botão Desselecionar: ${!!deselectBtn}`);
        console.log(`   - selectedBookmarkIds: ${!!selectedBookmarkIds}`);
        
        if (checkboxes.length === 0) {
            console.warn('⚠️ [SelectionFixTest] Nenhum checkbox encontrado - certifique-se de que há bookmarks visíveis');
        }
        
        if (!selectBtn || !deselectBtn) {
            console.error('❌ [SelectionFixTest] Botões não encontrados - problema no HTML');
        }
        
        if (!selectedBookmarkIds) {
            console.error('❌ [SelectionFixTest] selectedBookmarkIds não encontrado - problema na inicialização');
        }
    }

    // API pública
    window.SelectionFixTest = {
        simpleSelectAll,
        simpleDeselectAll,
        replaceButtonListeners,
        runCompleteTest,
        checkElements
    };

    console.log('[SelectionFixTest] Sistema carregado. Use SelectionFixTest.runCompleteTest() para testar.');

})();
