/**
 * Debug da Seleção Múltipla
 * Identifica e corrige problemas no sistema de seleção
 */

window.SelectionDebug = window.SelectionDebug || {};

(function() {
    'use strict';

    /**
     * Executa diagnóstico completo da seleção múltipla
     */
    function runDiagnostic() {
        console.log('=== DIAGNÓSTICO DA SELEÇÃO MÚLTIPLA ===');
        
        const results = {
            issues: [],
            warnings: [],
            info: []
        };

        // 1. Verificar se os botões existem
        checkButtons(results);
        
        // 2. Verificar se os event listeners estão funcionando
        checkEventListeners(results);
        
        // 3. Verificar se os checkboxes estão sendo criados corretamente
        checkCheckboxes(results);
        
        // 4. Verificar se a seleção com Shift funciona
        checkShiftSelection(results);
        
        // 5. Verificar sincronização entre sistemas
        checkSynchronization(results);

        // Mostrar resultados
        showResults(results);
        
        return results;
    }

    /**
     * Verifica se os botões de seleção existem e têm event listeners
     */
    function checkButtons(results) {
        const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
        const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");
        
        if (!selectAllBtn) {
            results.issues.push("❌ Botão 'Selecionar Todos' não encontrado");
        } else {
            results.info.push("✅ Botão 'Selecionar Todos' encontrado");
            
            // Verificar se tem event listener
            const listeners = getEventListeners(selectAllBtn);
            if (!listeners || !listeners.click || listeners.click.length === 0) {
                results.issues.push("❌ Botão 'Selecionar Todos' sem event listener");
            } else {
                results.info.push("✅ Botão 'Selecionar Todos' tem event listener");
            }
        }
        
        if (!deselectAllBtn) {
            results.issues.push("❌ Botão 'Desselecionar Todos' não encontrado");
        } else {
            results.info.push("✅ Botão 'Desselecionar Todos' encontrado");
            
            // Verificar se tem event listener
            const listeners = getEventListeners(deselectAllBtn);
            if (!listeners || !listeners.click || listeners.click.length === 0) {
                results.issues.push("❌ Botão 'Desselecionar Todos' sem event listener");
            } else {
                results.info.push("✅ Botão 'Desselecionar Todos' tem event listener");
            }
        }
    }

    /**
     * Verifica se os event listeners estão funcionando
     */
    function checkEventListeners(results) {
        // Verificar se UnifiedBookmarkSelection existe
        if (!window.UnifiedBookmarkSelection) {
            results.issues.push("❌ UnifiedBookmarkSelection não encontrado");
            return;
        }
        
        // Verificar se as funções principais existem
        const requiredFunctions = [
            'updateBookmarkSelection',
            'getSelectedBookmarkIds',
            'clearBookmarkSelection',
            'updateSelectedBookmarksCount'
        ];
        
        requiredFunctions.forEach(func => {
            if (typeof window.UnifiedBookmarkSelection[func] !== 'function') {
                results.issues.push(`❌ Função ${func} não encontrada`);
            } else {
                results.info.push(`✅ Função ${func} disponível`);
            }
        });
    }

    /**
     * Verifica se os checkboxes estão sendo criados corretamente
     */
    function checkCheckboxes(results) {
        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        
        if (checkboxes.length === 0) {
            results.warnings.push("⚠️ Nenhum checkbox de bookmark encontrado");
            return;
        }
        
        results.info.push(`✅ ${checkboxes.length} checkboxes encontrados`);
        
        // Verificar se os checkboxes têm event listeners
        let checkboxesWithListeners = 0;
        let checkboxesWithoutListeners = 0;
        
        checkboxes.forEach((checkbox, index) => {
            const listeners = getEventListeners(checkbox);
            if (listeners && listeners.change && listeners.change.length > 0) {
                checkboxesWithListeners++;
            } else {
                checkboxesWithoutListeners++;
                results.warnings.push(`⚠️ Checkbox ${index} sem event listener`);
            }
        });
        
        results.info.push(`✅ ${checkboxesWithListeners} checkboxes com event listeners`);
        if (checkboxesWithoutListeners > 0) {
            results.warnings.push(`⚠️ ${checkboxesWithoutListeners} checkboxes sem event listeners`);
        }
    }

    /**
     * Verifica se a seleção com Shift funciona
     */
    function checkShiftSelection(results) {
        const bookmarksContainer = document.getElementById("bookmarksContainer");
        
        if (!bookmarksContainer) {
            results.issues.push("❌ Container de bookmarks não encontrado");
            return;
        }
        
        // Verificar se tem event listener para Shift
        const listeners = getEventListeners(bookmarksContainer);
        if (!listeners || !listeners.click || listeners.click.length === 0) {
            results.issues.push("❌ Container de bookmarks sem event listener para Shift");
        } else {
            results.info.push("✅ Container de bookmarks tem event listener");
        }
        
        // Verificar se lastClickedBookmarkCheckbox existe
        if (typeof window.lastClickedBookmarkCheckbox === 'undefined') {
            results.warnings.push("⚠️ lastClickedBookmarkCheckbox não definido");
        } else {
            results.info.push("✅ lastClickedBookmarkCheckbox disponível");
        }
    }

    /**
     * Verifica sincronização entre sistemas
     */
    function checkSynchronization(results) {
        // Verificar se selectedBookmarkIds existe
        if (!window.selectedBookmarkIds) {
            results.issues.push("❌ selectedBookmarkIds global não encontrado");
        } else {
            results.info.push(`✅ selectedBookmarkIds: ${window.selectedBookmarkIds.size} selecionados`);
        }
        
        // Verificar se DynamicBookmarkRenderer existe
        if (window.DynamicBookmarkRenderer) {
            results.info.push("✅ DynamicBookmarkRenderer encontrado");
        } else {
            results.warnings.push("⚠️ DynamicBookmarkRenderer não encontrado");
        }
    }

    /**
     * Tenta obter event listeners de um elemento (funciona apenas no DevTools)
     */
    function getEventListeners(element) {
        try {
            // Esta função só funciona no DevTools
            return window.getEventListeners ? window.getEventListeners(element) : null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Mostra os resultados do diagnóstico
     */
    function showResults(results) {
        console.log('\n📊 RESULTADOS DO DIAGNÓSTICO:');
        
        if (results.issues.length > 0) {
            console.log('\n🚨 PROBLEMAS CRÍTICOS:');
            results.issues.forEach(issue => console.log(issue));
        }
        
        if (results.warnings.length > 0) {
            console.log('\n⚠️ AVISOS:');
            results.warnings.forEach(warning => console.log(warning));
        }
        
        if (results.info.length > 0) {
            console.log('\n✅ INFORMAÇÕES:');
            results.info.forEach(info => console.log(info));
        }
        
        console.log('\n=== FIM DO DIAGNÓSTICO ===');
    }

    /**
     * Testa a seleção de todos os bookmarks
     */
    function testSelectAll() {
        console.log('🧪 Testando seleção de todos...');
        
        const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
        if (selectAllBtn) {
            selectAllBtn.click();
            
            setTimeout(() => {
                const selectedCount = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
                const checkboxCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
                
                console.log(`📊 Resultado: ${selectedCount} no Set, ${checkboxCount} checkboxes marcados`);
                
                if (selectedCount === checkboxCount && selectedCount > 0) {
                    console.log('✅ Seleção de todos funcionando');
                } else {
                    console.log('❌ Problema na seleção de todos');
                }
            }, 100);
        } else {
            console.log('❌ Botão não encontrado');
        }
    }

    /**
     * Testa a desseleção de todos os bookmarks
     */
    function testDeselectAll() {
        console.log('🧪 Testando desseleção de todos...');
        
        const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");
        if (deselectAllBtn) {
            deselectAllBtn.click();
            
            setTimeout(() => {
                const selectedCount = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
                const checkboxCount = document.querySelectorAll('.bookmark-checkbox:checked').length;
                
                console.log(`📊 Resultado: ${selectedCount} no Set, ${checkboxCount} checkboxes marcados`);
                
                if (selectedCount === 0 && checkboxCount === 0) {
                    console.log('✅ Desseleção de todos funcionando');
                } else {
                    console.log('❌ Problema na desseleção de todos');
                }
            }, 100);
        } else {
            console.log('❌ Botão não encontrado');
        }
    }

    /**
     * Testa seleção manual de um checkbox
     */
    function testManualSelection() {
        console.log('🧪 Testando seleção manual...');

        const checkbox = document.querySelector('.bookmark-checkbox');
        if (checkbox) {
            const wasChecked = checkbox.checked;
            checkbox.checked = !wasChecked;
            checkbox.dispatchEvent(new Event('change'));

            setTimeout(() => {
                const selectedCount = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
                console.log(`📊 Checkbox ${wasChecked ? 'desmarcado' : 'marcado'}, Set tem ${selectedCount} itens`);
            }, 100);
        } else {
            console.log('❌ Nenhum checkbox encontrado');
        }
    }

    /**
     * Força a seleção de todos os bookmarks diretamente
     */
    function forceSelectAll() {
        console.log('🔧 Forçando seleção de todos...');

        if (!window.UnifiedBookmarkSelection) {
            console.error('❌ UnifiedBookmarkSelection não encontrado');
            return;
        }

        const checkboxes = document.querySelectorAll('.bookmark-checkbox');
        console.log(`📊 Encontrados ${checkboxes.length} checkboxes`);

        let processed = 0;
        checkboxes.forEach((checkbox, index) => {
            const bookmarkItem = checkbox.closest('.bookmark-item');
            if (bookmarkItem && bookmarkItem.dataset.id) {
                checkbox.checked = true;

                // Chamar diretamente a função de atualização
                if (window.UnifiedBookmarkSelection.updateBookmarkSelection) {
                    window.UnifiedBookmarkSelection.updateBookmarkSelection(bookmarkItem, true);
                    processed++;
                    console.log(`✅ Processado ${index}: ${bookmarkItem.dataset.id}`);
                }
            }
        });

        console.log(`📊 Processados ${processed} checkboxes`);

        // Atualizar contador
        if (window.UnifiedBookmarkSelection.updateSelectedBookmarksCount) {
            window.UnifiedBookmarkSelection.updateSelectedBookmarksCount();
        }

        const finalCount = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        console.log(`📊 Contagem final: ${finalCount}`);
    }

    /**
     * Força a desseleção de todos os bookmarks diretamente
     */
    function forceDeselectAll() {
        console.log('🔧 Forçando desseleção de todos...');

        if (!window.UnifiedBookmarkSelection) {
            console.error('❌ UnifiedBookmarkSelection não encontrado');
            return;
        }

        // Limpar diretamente
        if (window.UnifiedBookmarkSelection.clearBookmarkSelection) {
            window.UnifiedBookmarkSelection.clearBookmarkSelection();
        }

        const finalCount = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
        console.log(`📊 Contagem final: ${finalCount}`);
    }

    /**
     * Testa os botões diretamente
     */
    function testButtonsDirectly() {
        console.log('🧪 Testando botões diretamente...');

        const selectAllBtn = document.getElementById("selectAllBookmarksBtn");
        const deselectAllBtn = document.getElementById("deselectAllBookmarksBtn");

        console.log(`📊 selectAllBtn: ${!!selectAllBtn}`);
        console.log(`📊 deselectAllBtn: ${!!deselectAllBtn}`);

        if (selectAllBtn) {
            console.log('🔧 Simulando clique em selectAll...');
            selectAllBtn.click();

            setTimeout(() => {
                const count = window.selectedBookmarkIds ? window.selectedBookmarkIds.size : 0;
                console.log(`📊 Após selectAll: ${count} selecionados`);
            }, 200);
        }
    }

    /**
     * Testa o sistema de drag and drop
     */
    function testDragAndDrop() {
        console.log('🧪 Testando sistema de drag and drop...');

        // Verificar elementos draggable
        const draggableBookmarks = document.querySelectorAll('.bookmark-item[draggable="true"]');
        const draggableFolders = document.querySelectorAll('.folder-option[draggable="true"]');

        console.log(`📊 Elementos draggable encontrados:`);
        console.log(`   - Bookmarks: ${draggableBookmarks.length}`);
        console.log(`   - Folders: ${draggableFolders.length}`);

        // Verificar se initDragAndDrop existe
        if (typeof window.initDragAndDrop === 'function') {
            console.log('✅ initDragAndDrop disponível');
        } else {
            console.error('❌ initDragAndDrop não encontrado');
        }

        // Verificar containers
        const bookmarksContainer = document.getElementById('bookmarksContainer');
        const folderContainer = document.getElementById('folderCheckboxes');

        console.log(`📊 Containers:`);
        console.log(`   - bookmarksContainer: ${!!bookmarksContainer}`);
        console.log(`   - folderContainer: ${!!folderContainer}`);

        // Verificar se elementos têm event listeners de drag
        if (draggableBookmarks.length > 0) {
            const firstBookmark = draggableBookmarks[0];
            console.log(`📊 Primeiro bookmark draggable:`, firstBookmark);
            console.log(`   - ID: ${firstBookmark.dataset.id}`);
            console.log(`   - Draggable: ${firstBookmark.getAttribute('draggable')}`);
        }

        if (draggableFolders.length > 0) {
            const firstFolder = draggableFolders[0];
            console.log(`📊 Primeira pasta draggable:`, firstFolder);
            console.log(`   - Draggable: ${firstFolder.getAttribute('draggable')}`);
        }
    }

    // API pública
    window.SelectionDebug = {
        runDiagnostic,
        testSelectAll,
        testDeselectAll,
        testManualSelection,
        forceSelectAll,
        forceDeselectAll,
        testButtonsDirectly,
        testDragAndDrop
    };

    console.log('[SelectionDebug] Sistema carregado. Use SelectionDebug.runDiagnostic() para diagnosticar problemas.');

})();
